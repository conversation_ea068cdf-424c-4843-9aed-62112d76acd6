"""
Cloud-specific configuration for NewsMonitor application.
This module provides configuration overrides for cloud deployment.
"""

import os
import logging
from pathlib import Path
from google.cloud import secretmanager
import json

# Configure logging for cloud environment
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class CloudConfig:
    """Configuration class for cloud deployment."""
    
    def __init__(self):
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
        self.region = os.getenv('GOOGLE_CLOUD_LOCATION', 'us-central1')
        self.is_cloud_environment = os.getenv('DEPLOYMENT_ENVIRONMENT', '') == 'cloud'
        
        if self.is_cloud_environment:
            self.secret_client = secretmanager.SecretManagerServiceClient()
            logger.info(f"Initialized cloud config for project: {self.project_id}")
        else:
            self.secret_client = None
            logger.info("Running in local environment")
    
    def get_secret(self, secret_name: str, version: str = "latest") -> str:
        """Retrieve secret from Google Secret Manager."""
        if not self.is_cloud_environment:
            # Fallback to environment variables for local development
            return os.getenv(secret_name.upper().replace('-', '_'), '')
        
        try:
            name = f"projects/{self.project_id}/secrets/{secret_name}/versions/{version}"
            response = self.secret_client.access_secret_version(request={"name": name})
            return response.payload.data.decode("UTF-8")
        except Exception as e:
            logger.error(f"Failed to retrieve secret {secret_name}: {e}")
            return ""
    
    def get_json_secret(self, secret_name: str, version: str = "latest") -> dict:
        """Retrieve and parse JSON secret from Google Secret Manager."""
        secret_value = self.get_secret(secret_name, version)
        if secret_value:
            try:
                return json.loads(secret_value)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON secret {secret_name}: {e}")
        return {}
    
    def get_database_url(self) -> str:
        """Get database URL for cloud or local environment."""
        if self.is_cloud_environment:
            return self.get_secret("database-url")
        else:
            return os.getenv("DATABASE_URL", "postgresql://localhost/newsmonitor")
    
    def get_redis_url(self) -> str:
        """Get Redis URL for cloud or local environment."""
        if self.is_cloud_environment:
            # Redis IP is set via environment variable in Cloud Run
            redis_ip = os.getenv('REDIS_IP', 'localhost')
            return f"redis://{redis_ip}:6379/0"
        else:
            return os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0")
    
    def get_flask_secrets(self) -> dict:
        """Get Flask secret key and password salt."""
        if self.is_cloud_environment:
            return self.get_json_secret("flask-secrets")
        else:
            return {
                "secret-key": os.getenv("SECRET_KEY", "dev-secret-key"),
                "password-salt": os.getenv("SECURITY_PASSWORD_SALT", "dev-salt")
            }
    
    def get_llm_api_keys(self) -> dict:
        """Get LLM API keys."""
        if self.is_cloud_environment:
            return self.get_json_secret("llm-api-keys")
        else:
            return {
                "openai-key": os.getenv("OPENAI_API_KEY", ""),
                "anthropic-key": os.getenv("ANTHROPIC_API_KEY", ""),
                "gemini-key": os.getenv("GEMINI_API_KEY", "")
            }
    
    def get_email_config(self) -> dict:
        """Get email configuration."""
        if self.is_cloud_environment:
            return self.get_json_secret("email-config")
        else:
            return {
                "username": os.getenv("MAIL_USERNAME", ""),
                "password": os.getenv("MAIL_PASSWORD", ""),
                "default-sender": os.getenv("MAIL_DEFAULT_SENDER", "<EMAIL>")
            }
    
    def get_storage_bucket(self) -> str:
        """Get Cloud Storage bucket name."""
        if self.is_cloud_environment:
            return f"{self.project_id}-newsmonitor-storage"
        else:
            return ""
    
    def get_log_config(self) -> dict:
        """Get logging configuration for cloud environment."""
        if self.is_cloud_environment:
            return {
                "version": 1,
                "disable_existing_loggers": False,
                "formatters": {
                    "cloud": {
                        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                    }
                },
                "handlers": {
                    "console": {
                        "class": "logging.StreamHandler",
                        "formatter": "cloud",
                        "stream": "ext://sys.stdout"
                    }
                },
                "root": {
                    "level": "INFO",
                    "handlers": ["console"]
                }
            }
        else:
            # Return local logging config
            return {}

# Global cloud config instance
cloud_config = CloudConfig()

def get_cloud_config() -> CloudConfig:
    """Get the global cloud configuration instance."""
    return cloud_config

def is_cloud_environment() -> bool:
    """Check if running in cloud environment."""
    return cloud_config.is_cloud_environment
