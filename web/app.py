"""
Web interface for the NewsMonitor project.

This module provides a Flask web interface for displaying SP500 index graphs,
financial news, and predictions.
"""
import os
import sys
import argparse
from datetime import datetime, timedelta
import traceback
from flask import render_template, jsonify, request, redirect, url_for

from web import create_app
from web.data.prediction_service import get_prediction, get_llm_prediction
from web.data.news_data import get_all_news
from web.data.stock_data import get_stock_data, get_latest_price
from web.database import db
from utils.logging_config import get_web_logger
from web.config import HOST, PORT, DEBUG, TZ
from web.email_service import scheduler

# Authentication imports
from flask_login import current_user

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logger = get_web_logger(__name__)

app = create_app()

@app.route('/')
def index():
    """Render the News tab (main page)"""
    return render_template('index.html')

@app.route('/market-analysis')
def market_analysis():
    """Render the Market Analysis tab (authenticated users only)"""
    # Check if user is authenticated
    if not current_user.is_authenticated:
        return redirect(url_for('auth.login', next=request.url))

    return render_template('market_analysis.html')


@app.route('/admin')
def admin():
    """Render the Admin Dashboard (admin users only)"""
    # Check if user is authenticated and is admin
    if not current_user.is_authenticated:
        return redirect(url_for('auth.login', next=request.url))
    
    if not current_user.is_admin:
        return redirect(url_for('index'))

    return render_template('admin.html')


@app.route('/api/stock-data')
def stock_data():
    """API endpoint to get stock price data for any ticker"""
    
    ticker = request.args.get('ticker', 'SPY')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    interval = request.args.get('interval', '1d')
    period = request.args.get('period')
    
    if not period:
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if not start_date:
            start_date = end_date

    try:
        logger.info(f"Stock data API request: ticker={ticker}, start_date={start_date}, "
                    f"end_date={end_date}, interval={interval}, period={period}")
        
        # Fetch data using get_stock_data
        data = get_stock_data(ticker, start_date, end_date, interval=interval, period=period)
        
        # Check if we got any data
        if not data:
            logger.warning(f"No data returned for {ticker} from {start_date} to {end_date}")
            return jsonify({
                'error': f'No data available for {ticker} in the specified date range',
                'ticker': ticker,
                'dates': [],
                'open': [],
                'high': [],
                'low': [],
                'close': [],
                'volume': []
            }), 404

        logger.info(f"Stock data API response: {len(data['dates'])} data points for {ticker}")
        return jsonify(data)
        
    except Exception as e:
        logger.error(f"Stock data API endpoint error for {ticker}: {e}")
        print(traceback.format_exc())
        return jsonify({
            'error': f"Failed to fetch data for {ticker}: {str(e)}",
            'ticker': ticker,
            'dates': [],
            'open': [],
            'high': [],
            'low': [],
            'close': [],
            'volume': []
        }), 500

@app.route('/api/news')
def get_news():
    """
    Get news articles with optional filtering.
    Supports filtering by date range, and search keyword.
    """
    try:
        # Get query parameters
        limit = request.args.get('limit', default=20, type=int)
        start_date = request.args.get('start_date', default=None, type=str)
        end_date = request.args.get('end_date', default=None, type=str)
        search_keyword = request.args.get('search', default=None, type=str)
        source = request.args.get('source', default=None, type=str)
        sentiment = request.args.get('sentiment', default=None, type=str)
        offset = request.args.get('offset', default=0, type=int)
        exclude_ids = request.args.getlist('exclude_ids')


        # Log the request parameters
        logger.info(
            f"API request for news with limit: {limit}, date range: {start_date} to {end_date}, search: {search_keyword}, offset: {offset}")

        # Get news articles
        all_news_data = get_all_news(
            limit=limit,
            start_date=start_date,
            end_date=end_date,
            search_keyword=search_keyword,
            source=source,
            sentiment=sentiment,
            offset=offset,
            exclude_ids=exclude_ids
        )

        return jsonify(all_news_data)

    except Exception as e:
        logger.error(f"Error in get_news: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/prediction')
def prediction():
    """API endpoint to get the prediction for the next trading day's SPY price"""
    try:
        prediction_data = get_prediction()
        return jsonify(prediction_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/llm-prediction')
def llm_prediction():
    """API endpoint to get LLM-based market prediction"""
    try:
        # Get optional parameters
        preferred_api = request.args.get('api', 'gemini')
        force_update = request.args.get('force_update', 'false') == 'true'
        
        # Check if force_update is requested and user is not admin
        if force_update and (not current_user.is_authenticated or not current_user.is_admin):
            return jsonify({'error': 'Admin privileges required for force update'}), 403

        # Get LLM prediction (run async function in sync context)
        import asyncio
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        prediction_data = loop.run_until_complete(
            get_llm_prediction(preferred_api=preferred_api, force_update=force_update))
        return jsonify(prediction_data)
    except Exception as e:
        logger.error(f"Error in LLM prediction endpoint: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/prediction-history')
def prediction_history():
    """API endpoint to get historical prediction data"""
    from web.data.prediction_service import get_prediction_history

    # Get limit parameter (default to 10)
    limit = request.args.get('limit', default=10, type=int)

    try:
        history_data = get_prediction_history(limit=limit)
        return jsonify(history_data)
    except Exception as e:
        logger.error(f"Error getting prediction history: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/check-new-articles')
def check_new_articles():
    """API endpoint to check for new articles since last check"""
    try:
        # Get the last check timestamp from query parameter
        last_check = request.args.get('last_check', default=0, type=int)

        # For now, we'll implement a simple check based on article count
        # In a real implementation, you might check file modification times or database timestamps

        # Get current articles
        current_news = get_all_news(limit=50)  # Get recent articles
        current_count = len(current_news.get('all', []))

        # Simple heuristic: if we have more articles than expected, assume there are new ones
        # This is a basic implementation - you could enhance it with actual timestamp checking
        has_new_articles = current_count > 0

        return jsonify({
            'has_new_articles': has_new_articles,
            'new_article_count': current_count,
            'current_timestamp': int(datetime.now().timestamp()),
            'last_check_timestamp': last_check
        })

    except Exception as e:
        logger.error(f"Error checking for new articles: {e}")
        return jsonify({
            'has_new_articles': False,
            'new_article_count': 0,
            'current_timestamp': int(datetime.now().timestamp()),
            'error': str(e)
        }), 500


@app.route('/api/admin/users')
def admin_users():
    """API endpoint to get all users (admin only)"""
    # Check if user is authenticated and is admin
    if not current_user.is_authenticated or not current_user.is_admin:
        return jsonify({'error': 'Admin privileges required'}), 403

    try:
        from web.models import User
        
        # Get all users with relevant fields
        users = db.session.query(User).all()
        
        users_data = []
        for user in users:
            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_active': user.is_active,
                'is_admin': user.is_admin,
                'is_verified': user.is_verified,
                'created_at': user.created_at.isoformat() if user.created_at else None,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'email_preferences': user.email_preferences
            })
        
        return jsonify({
            'users': users_data,
            'total': len(users_data)
        })
        
    except Exception as e:
        logger.error(f"Error in admin users endpoint: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/send-email', methods=['POST'])
def send_email():
    """API endpoint to send emails (admin only)"""
    # Check if user is authenticated and is admin
    if not current_user.is_authenticated or not current_user.is_admin:
        return jsonify({'error': 'Admin privileges required'}), 403

    try:
        data = request.get_json()
        email_type = data.get('email_type')
        recipient_filter = data.get('recipient_filter')
        user_ids = data.get('user_ids', [])

        if not email_type:
            return jsonify({'error': 'Email type is required'}), 400

        # Import celery tasks
        from web.email_service.scheduler import send_targeted_emails_task
        
        # Trigger the celery task
        task = send_targeted_emails_task.delay(
            email_type=email_type,
            recipient_filter=recipient_filter,
            user_ids=user_ids
        )
        
        return jsonify({
            'message': 'Email task started successfully',
            'task_id': task.id,
            'email_type': email_type,
            'recipient_filter': recipient_filter,
            'user_count': len(user_ids) if recipient_filter == 'selected' else 'all'
        })
        
    except Exception as e:
        logger.error(f"Error in send email endpoint: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/admin/verify-user', methods=['POST'])
def admin_verify_user():
    """API endpoint to manually verify a user (admin only)"""
    # Check if user is authenticated and is admin
    if not current_user.is_authenticated or not current_user.is_admin:
        return jsonify({'error': 'Admin privileges required'}), 403

    try:
        data = request.get_json()
        user_id = data.get('user_id')

        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400

        from web.models import User
        from web.auth.utils import verify_user_email, log_security_event
        
        # Get the user
        user = db.session.query(User).filter(User.id == user_id).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        if user.is_verified:
            return jsonify({'error': 'User is already verified'}), 400

        # Verify the user
        success = verify_user_email(user)
        if success:
            # Log security event
            log_security_event(user.id, 'email_verified_by_admin',
                             details=f'Verified by admin {current_user.id}',
                             ip_address=request.remote_addr)
            
            return jsonify({
                'message': f'User {user.username} verified successfully',
                'user_id': user_id
            })
        else:
            return jsonify({'error': 'Failed to verify user'}), 500
            
    except Exception as e:
        logger.error(f"Error in admin verify user endpoint: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/available-dates')
def available_dates():
    """API endpoint to get available news dates for the date picker."""
    try:
        from web.data.news_data import get_available_dates

        # Get available dates from the news data
        dates = get_available_dates()

        # Convert to a dictionary format expected by the frontend
        # The frontend expects a dictionary where keys are dates
        date_dict = {date: True for date in dates}

        return jsonify(date_dict)

    except Exception as e:
        logger.error(f"Error getting available dates: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/market-overview')
def market_overview():
    """API endpoint to get market overview data for multiple tickers."""
    try:
        tickers = request.args.getlist('tickers')
        market_data = {}
        for ticker in tickers:
            start_date = datetime.now() - timedelta(days=7)
            end_date = datetime.now()
            data = get_stock_data(ticker, start_date, end_date)

            # Create market overview data
            if data and 'close' in data and data['close']:
                latest = data['close'][-1]
                previous = data['close'][-2] if len(data['close']) > 1 else latest
                change = latest - previous
                change_percent = (change / previous) * 100

                market_data[ticker] = {
                        'price': latest,
                        'change': change,
                        'changePercent': change_percent
                    }
        return jsonify(market_data)
    except Exception as e:
        logger.error(f"Error getting market overview: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/latest-price')
def latest_price():
    """API endpoint to get the latest price for a given ticker."""
    try:
        ticker = request.args.get('ticker')
        data = get_latest_price(ticker)
        if not data:
            return jsonify({'error': f'No data available for {ticker}'}), 404
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting latest price: {e}")
        return jsonify({'error': str(e)}), 500 


@app.route('/api/featured-news')
def featured_news():
    """API endpoint to get featured news articles based on influence score."""
    try:
        from web.data.news_data import get_featured_news

        # Get query parameters
        limit = request.args.get('limit', default=6, type=int)

        # Get featured news articles
        featured_news_data = get_featured_news(limit=limit)

        return jsonify(featured_news_data)

    except Exception as e:
        logger.error(f"Error getting featured news: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/is-market-open')
def is_market_open():
    """API endpoint to check if the market is open."""
    try:
        import exchange_calendars as ecals
        import pandas as pd

        code = request.args.get('code', default='XNYS', type=str)
        market = ecals.get_calendar(code)
        now = pd.Timestamp.now(tz=TZ)

        is_market_open = market.is_open_at_time(now, side="both", ignore_breaks=False)
        return jsonify({'isOpen': is_market_open})
    except Exception as e:
        logger.error(f"Error checking market hours: {e}")
        return jsonify({'error': str(e)}), 500

def is_port_in_use(host, port):
    """Check if a port is already in use."""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            s.bind((host, port))
            return False
    except socket.error:
        return True


def find_free_port(start_port, max_attempts=10):
    """Find a free port starting from start_port."""
    port = start_port
    for _ in range(max_attempts):
        if not is_port_in_use('0.0.0.0', port):
            return port
        port += 1
    return None


def main():
    """Entry point for the package."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description='Start the web server')
    parser.add_argument('--port', type=int, default=PORT,
                        help=f'Port to run the server on (default: {PORT})')
    parser.add_argument('--host', type=str, default=HOST,
                        help=f'Host to run the server on (default: {HOST})')
    parser.add_argument('--debug', action='store_true',
                        default=DEBUG, help='Run in debug mode')
    parser.add_argument('--no-reloader', action='store_true',
                        help='Disable the auto-reloader in debug mode')
    parser.add_argument('--force-port', action='store_true',
                        help='Force using the specified port even if it requires finding an alternative')
    args = parser.parse_args()

    # Check if the port is already in use
    if is_port_in_use(args.host, args.port):
        if args.force_port:
            # Try to find an alternative port
            free_port = find_free_port(args.port + 1)
            if free_port:
                logger.info(f"Using alternative port {free_port} instead")
                args.port = free_port
            else:
                logger.error(
                    "Could not find a free port. Please stop the existing server or specify a different port.")
                return 1
        else:
            logger.error(
                f"Port {args.port} is already in use. Please use --force-port to find an alternative port or specify a different port with --port.")
            return 1

    # Start the web server
    logger.info(f"Starting web server on {args.host}:{args.port}")
    try:
        # Configure Flask run options
        run_options = {
            'debug': args.debug,
            'host': args.host,
            'port': args.port
        }

        # If in debug mode, handle reloader settings
        if args.debug:
            if args.no_reloader:
                logger.info("Starting in debug mode with reloader disabled")
                run_options['use_reloader'] = False
            else:
                logger.info("Starting in debug mode with reloader enabled")
                run_options['threaded'] = False

        # Start the Flask app with the configured options
        app.run(**run_options)
        return 0
    except OSError as e:
        logger.error(f"Error starting server: {e}")
        return 1


@app.teardown_appcontext
def shutdown_session(exception=None):
    """Clean up database session after each request."""
    if exception:
        logger.warning(f"Request ended with exception: {exception}")
    db.session.remove()


if __name__ == '__main__':
    main()
