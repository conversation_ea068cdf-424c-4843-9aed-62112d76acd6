Flask>=2.0.0
yfinance>=0.2.0
pandas>=1.3.0
plotly>=5.0.0
python-dateutil>=2.8.0
requests>=2.25.0

# Authentication and Security
Flask-Login>=0.6.0
Flask-WTF>=1.1.0
WTForms>=3.0.0
bcrypt>=4.0.0
email-validator>=2.0.0

# Email functionality
Flask-Mail>=0.9.1

# Background tasks
Celery>=5.3.0
redis>=4.5.0

# Database
SQLAlchemy>=2.0.0
Flask-SQLAlchemy>=3.0.0
Flask-Migrate>=4.0.0
Flask-Cors>=6.0.0

# Google cloud
gunicorn==21.2.0
google-cloud-tasks==2.14.1
google-cloud-sql-connector==1.4.0

# Pytorch
# Core dependencies with specific versions for compatibility
torch>=2.0.1
transformers>=4.30.2
datasets>=2.14.5
numpy>=1.24.3  # This version is compatible with PyTorch 2.0.1
pandas>=1.3.0
scikit-learn>=1.0.0
# tiktoken removed, using transformers tokenizers instead
matplotlib>=3.4.0
tqdm>=4.60.0
accelerate>=0.20.0
sentencepiece>=0.1.99
tensorboard>=2.10.0


