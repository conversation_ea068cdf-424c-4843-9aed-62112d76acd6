"""
Configuration settings for the web interface.

This module provides configuration settings for the web interface,
including paths, URLs, and other settings.
"""

import os
from pathlib import Path
from zoneinfo import ZoneInfo
from celery.schedules import crontab
from cloud.cloud_config import get_cloud_config

# Project paths
ROOT_DIR = Path(__file__).parent.resolve()
TEMPLATES_DIR = ROOT_DIR / "templates"
STATIC_DIR = ROOT_DIR / "static"
LOG_DIR = ROOT_DIR / "logs"

# Ensure directories exist
os.makedirs(TEMPLATES_DIR, exist_ok=True)
os.makedirs(STATIC_DIR, exist_ok=True)
os.makedirs(LOG_DIR, exist_ok=True)

# Web server settings
HOST = "0.0.0.0"
PORT = 5000
DEBUG = False

# Data settings
DEFAULT_LOOKBACK_DAYS = 30  # Default number of days to look back for data
DEFAULT_ARTICLE_LIMIT = 20  # Default number of articles to return

# Date format
DATE_FORMAT = "%Y-%m-%d"  # YYYY-MM-DD
DATE_TIME_FORMAT = "%Y-%m-%d %H:%M"  # YYYY-MM-DD HH:MM
DISPLAY_DATE_FORMAT = "%b %d, %Y"  # e.g., Jan 01, 2023
TZ = ZoneInfo("America/New_York")

cloud_config = get_cloud_config()
class Config:
    """Configuration class for web interface settings."""
    # Application settings
    BASE_URL = os.environ.get('BASE_URL', 'http://localhost:5000')
    SERVER_NAME = os.environ.get('SERVER_NAME', 'localhost')
    PREFERRED_URL_SCHEME = os.environ.get('PREFERRED_URL_SCHEME', 'http')
    SECRET_KEY = cloud_config.get_flask_secrets().get('secret-key')
    SECURITY_PASSWORD_SALT = cloud_config.get_flask_secrets().get('password-salt')
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None  # No time limit for CSRF tokens

    # Database settings
    DATABASE_URL = cloud_config.get_database_url()
    SQLALCHEMY_DATABASE_URI = DATABASE_URL
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,
        'max_overflow': 20,
        'pool_pre_ping': True,
        'echo': False  # Set to True for SQL debugging
    }

    # Email settings
    email_config = cloud_config.get_email_config()
    MAIL_USERNAME = email_config.get('username')
    MAIL_PASSWORD = email_config.get('password')
    MAIL_DEFAULT_SENDER = email_config.get('default-sender')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_USE_TLS = True
    MAIL_USE_SSL = False

    # Celery settings
    ENABLE_CELERY = False
    CELERY_BROKER_URL = cloud_config.get_redis_url()
    CELERY_RESULT_BACKEND = cloud_config.get_redis_url()
    CELERY_TIMEZONE = 'America/New_York'
    CELERY_ENABLE_UTC = False
    CELERY_BEAT_SCHEDULE = {
        "send-daily-summary": {
            "task": "web.email_service.scheduler.send_daily_summaries_task",
            "schedule": crontab(hour=10, minute=0),  # every day at 10 AM ET
            'options': {'expires': 60.0 * 60.0 * 2.0}  # Expire after 2 hours
        },
    }
    CELERY_BEAT_SCHEDULE_FILENAME = ROOT_DIR.parent / 'data' / "celerybeat-schedule"



