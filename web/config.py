"""
Configuration settings for the web interface.

This module provides configuration settings for the web interface,
including paths, URLs, and other settings.
"""

import os
from pathlib import Path
from zoneinfo import ZoneInfo
from celery.schedules import crontab

# Project paths
ROOT_DIR = Path(__file__).parent.resolve()
TEMPLATES_DIR = ROOT_DIR / "templates"
STATIC_DIR = ROOT_DIR / "static"
LOG_DIR = ROOT_DIR / "logs"

# Ensure directories exist
os.makedirs(TEMPLATES_DIR, exist_ok=True)
os.makedirs(STATIC_DIR, exist_ok=True)
os.makedirs(LOG_DIR, exist_ok=True)

# Web server settings
HOST = "0.0.0.0"
PORT = 5000
DEBUG = False

# Data settings
DEFAULT_LOOKBACK_DAYS = 30  # Default number of days to look back for data
DEFAULT_ARTICLE_LIMIT = 20  # Default number of articles to return

# Date format
DATE_FORMAT = "%Y-%m-%d"  # YYYY-MM-DD
DATE_TIME_FORMAT = "%Y-%m-%d %H:%M"  # YYYY-MM-DD HH:MM
DISPLAY_DATE_FORMAT = "%b %d, %Y"  # e.g., Jan 01, 2023
TZ = ZoneInfo("America/New_York")

class Config:
    """Configuration class for web interface settings."""
    # Application settings
    BASE_URL = os.environ.get('BASE_URL', 'http://localhost:5000')
    SERVER_NAME = os.environ.get('SERVER_NAME', 'localhost')
    PREFERRED_URL_SCHEME = os.environ.get('PREFERRED_URL_SCHEME', 'http')
    SECRET_KEY = os.environ.get(
            'SECRET_KEY', 'dev-secret-key-change-in-production')
    SECURITY_PASSWORD_SALT = os.environ.get(
            'SECURITY_PASSWORD_SALT', 'dev-salt-change-in-production')
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None  # No time limit for CSRF tokens

    # Email settings
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get(
        'MAIL_DEFAULT_SENDER', '<EMAIL>')
    MAIL_USE_TLS = True
    MAIL_USE_SSL = False

    # Celery settings
    CELERY_BROKER_URL = os.environ.get(
        'CELERY_BROKER_URL', 'redis://localhost:6379/0')
    CELERY_RESULT_BACKEND = os.environ.get(
        'CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
    CELERY_TIMEZONE = 'America/New_York'
    CELERY_ENABLE_UTC = False
    CELERY_BEAT_SCHEDULE = {
        "send-daily-summary": {
            "task": "web.email_service.scheduler.send_daily_summaries_task",
            "schedule": crontab(hour=10, minute=0),  # every day at 10 AM ET
            'options': {'expires': 60.0 * 60.0 * 2.0}  # Expire after 2 hours
        },
    }
    CELERY_BEAT_SCHEDULE_FILENAME = ROOT_DIR.parent / 'data' / "celerybeat-schedule"



