"""
NewsMonitor - Web Interface Package

A Flask-based web interface for displaying SP500 index graphs and financial news.
"""

__version__ = "0.1.0"

from flask import Flask
from celery import Celery
from flask_login import <PERSON>ginManager
from flask_sqlalchemy import SQLAlchemy
from celery.schedules import crontab

from web.email_service.service import EmailService
from web.models import User

celery = Celery(__name__)
db = SQLAlchemy()
email_service = EmailService()
login_manager = LoginManager()

def create_app(config_class="web.config.Config"):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize Flask-SQLAlchemy
    db.init_app(app)

    # Initialize Flask-Login
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))

    # Initialize email service
    email_service.init_app(app)

    # Initialize Celery with Flask config
    init_celery(app)

    # Register authentication blueprint
    from web.auth.routes import auth_bp
    app.register_blueprint(auth_bp)

    # Register health check blueprint
    from cloud.health_endpoints import health_bp
    app.register_blueprint(health_bp)

    return app

def init_celery(app=None):
    app = app or create_app()
    celery.conf.broker_url = app.config['CELERY_BROKER_URL']
    celery.conf.result_backend = app.config['CELERY_RESULT_BACKEND']
    celery.conf.timezone = app.config['CELERY_TIMEZONE']
    celery.conf.enable_utc = app.config['CELERY_ENABLE_UTC']
    celery.conf.beat_schedule = app.config['CELERY_BEAT_SCHEDULE']
    celery.conf.beat_schedule_filename = app.config['CELERY_BEAT_SCHEDULE_FILENAME']

    class ContextTask(celery.Task):
        """Make celery tasks work with Flask app context."""

        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)

    celery.Task = ContextTask
