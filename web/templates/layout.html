<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Market Monitor{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>

    <!-- Animate.css for animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/news-cards.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/news.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/market-analysis.css') }}">

    {% block head %}{% endblock %}
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">Market Monitor</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/" id="news-tab">Financial News</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/market-analysis" id="market-analysis-tab">Market Analysis</a>
                    </li>
                    {% if current_user.is_authenticated and current_user.is_admin %}
                    <li class="nav-item">
                        <a class="nav-link" href="/admin" id="admin-tab">Admin</a>
                    </li>
                    {% endif %}
                </ul>
                <div class="ms-auto d-flex align-items-center">

                    <!-- User Authentication Menu -->
                    {% if current_user.is_authenticated %}
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle"></i>
                            {{ current_user.first_name or current_user.username }}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <h6 class="dropdown-header">{{ current_user.email }}</h6>
                            </li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                    <i class="bi bi-person me-2"></i>Profile
                                </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                    <i class="bi bi-key me-2"></i>Change Password
                                </a></li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                    <i class="bi bi-box-arrow-right me-2"></i>Logout
                                </a></li>
                        </ul>
                    </div>
                    {% else %}
                    <div class="d-flex gap-2">
                        <a href="{{ url_for('auth.login') }}" class="btn btn-outline-light">
                            <i class="bi bi-box-arrow-in-right"></i> Login
                        </a>
                        <a href="{{ url_for('auth.register') }}" class="btn btn-light">
                            <i class="bi bi-person-plus"></i> Sign Up
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Content container with conditional padding for seamless market overview -->
    <div class="content-container">
        {% block content %}{% endblock %}
    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">Market Monitor &copy; 2025</span>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Plotly.js for charts -->
    <script src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>

    <!-- Tab Navigation Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Handle Market Analysis tab authentication check
            const marketAnalysisTab = document.getElementById('market-analysis-tab');
            if (marketAnalysisTab) {
                marketAnalysisTab.addEventListener('click', function (e) {
                    // Check if user is authenticated
                    const isAuthenticated = {{ 'true' if current_user.is_authenticated else 'false' }};
                if (!isAuthenticated) {
                    e.preventDefault();
                    // Show login modal or redirect to login
                    alert('Please log in to access Market Analysis');
                    window.location.href = '{{ url_for("auth.login") }}';
                    return false;
                }
            });
            }

        // Set active tab based on current page
        const currentPath = window.location.pathname;
        const newsTab = document.getElementById('news-tab');
        const marketTab = document.getElementById('market-analysis-tab');
        const adminTab = document.getElementById('admin-tab');

        // Remove active class from all tabs
        [newsTab, marketTab, adminTab].forEach(tab => {
            if (tab) tab.classList.remove('active');
        });

        // Add active class to current tab
        if (currentPath === '/market-analysis') {
            marketTab?.classList.add('active');
        } else if (currentPath === '/admin') {
            adminTab?.classList.add('active');
        } else {
            newsTab?.classList.add('active');
        }
        });
    </script>

    <!-- Custom JS -->
    {% block scripts %}{% endblock %}
</body>

</html>