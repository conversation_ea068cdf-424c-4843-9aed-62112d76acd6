{% extends "layout.html" %}

{% block title %}Admin Dashboard - Market Monitor{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/auth.css') }}">
<style>
.admin-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.admin-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.admin-section h3 {
    color: #1f2937;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.admin-section h3 i {
    color: #3b82f6;
}

.admin-form {
    display: grid;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-group select,
.form-group input {
    padding: 10px 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group select:focus,
.form-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.admin-btn:hover {
    background: #2563eb;
}

.admin-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

.admin-btn.danger {
    background: #ef4444;
}

.admin-btn.danger:hover {
    background: #dc2626;
}

.admin-btn.success {
    background: #10b981;
}

.admin-btn.success:hover {
    background: #059669;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.users-table th,
.users-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.users-table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.users-table tbody tr:hover {
    background: #f9fafb;
}

.user-checkbox {
    width: 16px;
    height: 16px;
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-active {
    background: #10b981;
}

.status-inactive {
    background: #ef4444;
}

.admin-badge {
    background: #3b82f6;
    color: white;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.verified-badge {
    background: #10b981;
    color: white;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.unverified-badge {
    background: #ef4444;
    color: white;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.btn-verify {
    background: #10b981;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-verify:hover {
    background: #059669;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.alert {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: none;
}

.alert-success {
    background: #dcfce7;
    border: 1px solid #bbf7d0;
    color: #166534;
}

.alert-error {
    background: #fee2e2;
    border: 1px solid #fecaca;
    color: #991b1b;
}

.alert-info {
    background: #dbeafe;
    border: 1px solid #bfdbfe;
    color: #1e40af;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .admin-container {
        padding: 15px;
    }
    
    .admin-section {
        padding: 20px;
    }
    
    .users-table {
        font-size: 0.85rem;
    }
    
    .users-table th,
    .users-table td {
        padding: 8px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="admin-container">
    <h2 style="color: #1f2937; font-weight: 700; margin-bottom: 30px; display: flex; align-items: center; gap: 10px;">
        <i class="bi bi-gear-fill" style="color: #3b82f6;"></i>
        Admin Dashboard
    </h2>

    <!-- Alerts -->
    <div id="alert-container">
        <div id="success-alert" class="alert alert-success">
            <i class="bi bi-check-circle"></i>
            <span id="success-message"></span>
        </div>
        <div id="error-alert" class="alert alert-error">
            <i class="bi bi-exclamation-triangle"></i>
            <span id="error-message"></span>
        </div>
        <div id="info-alert" class="alert alert-info">
            <i class="bi bi-info-circle"></i>
            <span id="info-message"></span>
        </div>
    </div>

    <!-- Market Analysis Controls -->
    <div class="admin-section">
        <h3>
            <i class="bi bi-graph-up"></i>
            Market Analysis Controls
        </h3>
        <div class="admin-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="api-provider">API Provider</label>
                    <select id="api-provider" name="api-provider">
                        <option value="gemini">Google Gemini</option>
                        <option value="anthropic">Anthropic Claude</option>
                        <option value="openai">OpenAI GPT</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>&nbsp;</label>
                    <button type="button" id="force-update-btn" class="admin-btn">
                        <i class="bi bi-arrow-clockwise"></i>
                        Force Update Analysis
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Management -->
    <div class="admin-section">
        <h3>
            <i class="bi bi-envelope"></i>
            Email Management
        </h3>
        <div class="admin-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="email-type">Email Type</label>
                    <select id="email-type" name="email-type">
                        <option value="daily_summary">Daily Market Summary</option>
                        <option value="market_alert">Market Alert</option>
                        <option value="test_email">Test Email</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="recipient-filter">Send To</label>
                    <select id="recipient-filter" name="recipient-filter">
                        <option value="selected">Selected Users</option>
                        <option value="all_active">All Active Users</option>
                        <option value="all_subscribed">All Subscribed Users</option>
                        <option value="admins_only">Admins Only</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <button type="button" id="send-email-btn" class="admin-btn">
                        <i class="bi bi-send"></i>
                        Send Email
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- User Management -->
    <div class="admin-section">
        <h3>
            <i class="bi bi-people"></i>
            User Management
        </h3>
        <div class="admin-form">
            <div class="form-row">
                <div class="form-group">
                    <button type="button" id="refresh-users-btn" class="admin-btn">
                        <i class="bi bi-arrow-clockwise"></i>
                        Refresh User List
                    </button>
                </div>
                <div class="form-group">
                    <button type="button" id="select-all-users-btn" class="admin-btn">
                        <i class="bi bi-check-all"></i>
                        Select All
                    </button>
                </div>
                <div class="form-group">
                    <button type="button" id="deselect-all-users-btn" class="admin-btn">
                        <i class="bi bi-x-square"></i>
                        Deselect All
                    </button>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div style="overflow-x: auto;">
            <table class="users-table">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="select-all-checkbox" class="user-checkbox"></th>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Name</th>
                        <th>Status</th>
                        <th>Verified</th>
                        <th>Role</th>
                        <th>Created</th>
                        <th>Last Login</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="users-table-body">
                    <!-- Users will be loaded dynamically -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Loading overlay -->
<div class="loading-overlay" id="loading-overlay">
    <div class="loading-spinner"></div>
</div>

<script>
class AdminDashboard {
    constructor() {
        this.selectedUsers = new Set();
        this.users = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadUsers();
    }

    setupEventListeners() {
        // Force update analysis
        document.getElementById('force-update-btn').addEventListener('click', () => {
            this.forceUpdateAnalysis();
        });

        // Send email
        document.getElementById('send-email-btn').addEventListener('click', () => {
            this.sendEmail();
        });

        // User management
        document.getElementById('refresh-users-btn').addEventListener('click', () => {
            this.loadUsers();
        });

        document.getElementById('select-all-users-btn').addEventListener('click', () => {
            this.selectAllUsers();
        });

        document.getElementById('deselect-all-users-btn').addEventListener('click', () => {
            this.deselectAllUsers();
        });

        // Master checkbox
        document.getElementById('select-all-checkbox').addEventListener('change', (e) => {
            if (e.target.checked) {
                this.selectAllUsers();
            } else {
                this.deselectAllUsers();
            }
        });
    }

    showLoading() {
        document.getElementById('loading-overlay').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loading-overlay').style.display = 'none';
    }

    showAlert(type, message) {
        // Hide all alerts first
        document.querySelectorAll('.alert').forEach(alert => alert.style.display = 'none');
        
        // Show the specific alert
        const alert = document.getElementById(`${type}-alert`);
        const messageElement = document.getElementById(`${type}-message`);
        
        if (alert && messageElement) {
            messageElement.textContent = message;
            alert.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }
    }

    async forceUpdateAnalysis() {
        try {
            this.showLoading();
            const apiProvider = document.getElementById('api-provider').value;
            
            const response = await fetch(`/api/llm-prediction?api=${apiProvider}&force_update=true`);
            const data = await response.json();
            
            if (response.ok) {
                this.showAlert('success', 'Analysis updated successfully!');
            } else {
                this.showAlert('error', data.error || 'Failed to update analysis');
            }
        } catch (error) {
            this.showAlert('error', 'Error updating analysis: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async sendEmail() {
        try {
            const emailType = document.getElementById('email-type').value;
            const recipientFilter = document.getElementById('recipient-filter').value;
            
            let userIds = [];
            
            if (recipientFilter === 'selected') {
                userIds = Array.from(this.selectedUsers);
                if (userIds.length === 0) {
                    this.showAlert('error', 'Please select at least one user');
                    return;
                }
            }
            
            this.showLoading();
            
            const response = await fetch('/api/send-email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email_type: emailType,
                    recipient_filter: recipientFilter,
                    user_ids: userIds
                })
            });
            
            const data = await response.json();
            
            if (response.ok) {
                this.showAlert('success', `Email sent successfully! ${data.message || ''}`);
            } else {
                this.showAlert('error', data.error || 'Failed to send email');
            }
        } catch (error) {
            this.showAlert('error', 'Error sending email: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async loadUsers() {
        try {
            this.showLoading();
            
            const response = await fetch('/api/admin/users');
            const data = await response.json();
            
            if (response.ok) {
                this.users = data.users || [];
                this.renderUsers();
                this.showAlert('info', `Loaded ${this.users.length} users`);
            } else {
                this.showAlert('error', data.error || 'Failed to load users');
            }
        } catch (error) {
            this.showAlert('error', 'Error loading users: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    renderUsers() {
        const tbody = document.getElementById('users-table-body');
        tbody.innerHTML = '';

        this.users.forEach(user => {
            const row = document.createElement('tr');
            
            const statusClass = user.is_active ? 'status-active' : 'status-inactive';
            const statusText = user.is_active ? 'Active' : 'Inactive';
            const adminBadge = user.is_admin ? '<span class="admin-badge">ADMIN</span>' : '';
            const verifiedBadge = user.is_verified ? 
                '<span class="verified-badge">VERIFIED</span>' : 
                '<span class="unverified-badge">UNVERIFIED</span>';
            const createdDate = new Date(user.created_at).toLocaleDateString();
            const lastLogin = user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never';
            const verifyButton = !user.is_verified ? 
                `<button class="btn-verify" onclick="window.adminDashboard.verifyUser(${user.id})">Verify</button>` : '';
            
            row.innerHTML = `
                <td>
                    <input type="checkbox" class="user-checkbox" value="${user.id}" 
                           ${this.selectedUsers.has(user.id) ? 'checked' : ''}>
                </td>
                <td>${user.username}</td>
                <td>${user.email}</td>
                <td>${user.first_name || ''} ${user.last_name || ''}</td>
                <td>
                    <span class="status-indicator ${statusClass}"></span>
                    ${statusText}
                </td>
                <td>${verifiedBadge}</td>
                <td>${adminBadge}</td>
                <td>${createdDate}</td>
                <td>${lastLogin}</td>
                <td>${verifyButton}</td>
            `;
            
            // Add event listener to checkbox
            const checkbox = row.querySelector('.user-checkbox');
            checkbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.selectedUsers.add(parseInt(user.id));
                } else {
                    this.selectedUsers.delete(parseInt(user.id));
                }
                this.updateMasterCheckbox();
            });
            
            tbody.appendChild(row);
        });
    }

    selectAllUsers() {
        this.selectedUsers.clear();
        this.users.forEach(user => {
            this.selectedUsers.add(parseInt(user.id));
        });
        this.updateCheckboxes();
    }

    deselectAllUsers() {
        this.selectedUsers.clear();
        this.updateCheckboxes();
    }

    updateCheckboxes() {
        document.querySelectorAll('.user-checkbox').forEach(checkbox => {
            if (checkbox.id !== 'select-all-checkbox') {
                checkbox.checked = this.selectedUsers.has(parseInt(checkbox.value));
            }
        });
        this.updateMasterCheckbox();
    }

    updateMasterCheckbox() {
        const masterCheckbox = document.getElementById('select-all-checkbox');
        const totalUsers = this.users.length;
        const selectedCount = this.selectedUsers.size;
        
        if (selectedCount === 0) {
            masterCheckbox.checked = false;
            masterCheckbox.indeterminate = false;
        } else if (selectedCount === totalUsers) {
            masterCheckbox.checked = true;
            masterCheckbox.indeterminate = false;
        } else {
            masterCheckbox.checked = false;
            masterCheckbox.indeterminate = true;
        }
    }

    async verifyUser(userId) {
        try {
            this.showLoading();
            
            const response = await fetch('/api/admin/verify-user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: userId
                })
            });
            
            const data = await response.json();
            
            if (response.ok) {
                this.showAlert('success', `User verified successfully!`);
                // Refresh the user list to show updated status
                this.loadUsers();
            } else {
                this.showAlert('error', data.error || 'Failed to verify user');
            }
        } catch (error) {
            this.showAlert('error', 'Error verifying user: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }
}

// Initialize admin dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.adminDashboard = new AdminDashboard();
});
</script>
{% endblock %}