from typing import List
from web import db
from web.models import EmailLog, User

from utils.logging_config import get_web_logger
logger = get_web_logger(__name__)

def get_users_for_daily_summary() -> List[int]:
    """Get all user IDs who should receive daily summaries."""
    try:
        user_ids = db.session.query(User.id).filter(
            User.is_active == True,
            User.is_verified == True,
            User.email_preferences['market_summary'].as_boolean() == True,
            User.email_preferences['frequency'].as_string() == 'daily'
        ).all()
        return [user_id[0] for user_id in user_ids]
    except Exception as e:
        logger.error(f"Error getting users for daily summary: {e}")
        return []


def get_users_for_weekly_summary() -> List[int]:
    """Get all user IDs who should receive weekly summaries."""
    try:
        user_ids = db.session.query(User.id).filter(
            User.is_active == True,
            User.is_verified == True,
            User.email_preferences['market_summary'].as_boolean() == True,
            User.email_preferences['frequency'].as_string() == 'weekly'
        ).all()
        return [user_id[0] for user_id in user_ids]
    except Exception as e:
        logger.error(f"Error getting users for weekly summary: {e}")
        return []


def get_users_for_market_alerts() -> List[int]:
    """Get all user IDs who should receive market alerts."""
    try:
        user_ids = db.session.query(User.id).filter(
            User.is_active == True,
            User.is_verified == True,
            User.email_preferences['market_alerts'].as_boolean() == True
        ).all()
        return [user_id[0] for user_id in user_ids]
    except Exception as e:
        logger.error(f"Error getting users for market alerts: {e}")
        return []
    
def get_users_for_email_type(email_type: str, recipient_filter: str, user_ids: List[int] = None) -> List[int]:
    """Celery task to send targeted emails to specific users or groups."""
    try:
        logger.info(f"Starting targeted emails task: {email_type} to {recipient_filter}")
        target_users = []
        # Determine which users to send to based on recipient_filter
        if recipient_filter == 'selected':
            if not user_ids:
                logger.error("No user IDs provided for selected users filter")
            target_users = user_ids
            
        elif recipient_filter == 'all_active':
            # Get all active users
            user_query_result = db.session.query(User.id).filter(
                User.is_active == True
            ).all()
            target_users = [user_id[0] for user_id in user_query_result]
            
        elif recipient_filter == 'all_subscribed':
            # Get all users subscribed to the specific email type
            if email_type == 'daily_summary':
                target_users = get_users_for_daily_summary()
            elif email_type == 'market_alert':
                target_users = get_users_for_market_alerts()
                
        elif recipient_filter == 'admins_only':
            # Get all admin users
            user_query_result = db.session.query(User.id).filter(
                User.is_active == True,
                User.is_admin == True
            ).all()
            target_users = [user_id[0] for user_id in user_query_result]
            
        else:
            logger.error(f"Unknown recipient filter: {recipient_filter}")
        
        if not target_users:
            logger.info(f"No users found for filter: {recipient_filter}")
        
        logger.info(f"Sending {email_type} emails to {len(target_users)} users")

        return target_users
        
    except Exception as e:
        logger.error(f"Error in targeted emails task: {e}")
        return []



