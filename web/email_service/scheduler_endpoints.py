"""
Cloud Scheduler HTTP endpoints.
These endpoints handle background tasks triggered by Cloud Scheduler.
"""

import time
from datetime import datetime, timedelta
from typing import List, Dict, Any
from flask import Blueprint, request, jsonify, current_app
from functools import wraps

from web.email_service.service import EmailService, TZ
from web.email_service.scheduler_helper import (
    get_users_for_daily_summary,
    get_users_for_weekly_summary,
    get_users_for_market_alerts,
    get_users_for_email_type
)
from web.models import EmailLog, User
from web import db
from utils.logging_config import get_web_logger

logger = get_web_logger(__name__)

scheduler_bp = Blueprint('scheduler', __name__, url_prefix='/api/scheduler')

def require_scheduler_auth(f):
    """Decorator to ensure requests come from Cloud Scheduler or admin."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Check for Cloud Scheduler headers
        scheduler_headers = [
            'X-CloudScheduler',
            'X-CloudScheduler-JobName',
            'X-CloudScheduler-ScheduleTime'
        ]
        
        # Allow requests with Cloud Scheduler headers
        if any(header in request.headers for header in scheduler_headers):
            return f(*args, **kwargs)
        
        # Allow requests with admin authentication
        from flask_login import current_user
        if current_user.is_authenticated and current_user.is_admin:
            return f(*args, **kwargs)
        
        # Allow requests from internal service accounts (check User-Agent)
        user_agent = request.headers.get('User-Agent', '')
        if 'Google-Cloud-Scheduler' in user_agent:
            return f(*args, **kwargs)
        
        logger.warning(f"Unauthorized scheduler request from {request.remote_addr}")
        return jsonify({'error': 'Unauthorized'}), 401
    
    return decorated_function

@scheduler_bp.route('/daily-summary', methods=['POST'])
@require_scheduler_auth
def send_daily_summaries():
    """HTTP endpoint to send daily summaries to all subscribed users."""
    try:
        logger.info("Starting daily summaries task")
        
        # Get all active users who want daily summaries
        users = get_users_for_daily_summary()
        
        if not users:
            logger.info("No users found for daily summaries")
            return jsonify({"status": "success", "users_processed": 0})
        
        email_service = EmailService(current_app)
        success_count = 0
        error_count = 0
        
        # Generate summary data
        summary_data = email_service.generate_daily_summary_data(days_back=3)
        
        for user_id in users:
            try:
                if email_service.send_market_summary(user_id, summary_data):
                    success_count += 1
                    logger.info(f"Daily summary sent to user {user_id}")
                else:
                    error_count += 1
                    logger.error(f"Failed to send daily summary to user {user_id}")
                
                # Small delay to avoid overwhelming the email server
                time.sleep(1)
                
            except Exception as e:
                error_count += 1
                logger.error(f"Error sending daily summary to user {user_id}: {e}")
        
        logger.info(f"Daily summaries task completed: {success_count} sent, {error_count} failed")
        
        return jsonify({
            "status": "success",
            "users_processed": len(users),
            "emails_sent": success_count,
            "errors": error_count
        })
        
    except Exception as e:
        logger.error(f"Error in daily summaries task: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@scheduler_bp.route('/weekly-summary', methods=['POST'])
@require_scheduler_auth
def send_weekly_summaries():
    """HTTP endpoint to send weekly summaries to subscribed users."""
    try:
        logger.info("Starting weekly summaries task")
        
        # Get all active users who want weekly summaries
        users = get_users_for_weekly_summary()
        
        if not users:
            logger.info("No users found for weekly summaries")
            return jsonify({"status": "success", "users_processed": 0})
        
        email_service = EmailService(current_app)
        success_count = 0
        error_count = 0
        
        # Generate weekly summary data
        summary_data = email_service.generate_daily_summary_data(days_back=7)
        
        for user_id in users:
            try:
                if email_service.send_market_summary(user_id, summary_data):
                    success_count += 1
                    logger.info(f"Weekly summary sent to user {user_id}")
                else:
                    error_count += 1
                    logger.error(f"Failed to send weekly summary to user {user_id}")
                
                # Small delay to avoid overwhelming the email server
                time.sleep(1)
                
            except Exception as e:
                error_count += 1
                logger.error(f"Error sending weekly summary to user {user_id}: {e}")
        
        logger.info(f"Weekly summaries task completed: {success_count} sent, {error_count} failed")
        
        return jsonify({
            "status": "success",
            "users_processed": len(users),
            "emails_sent": success_count,
            "errors": error_count
        })
        
    except Exception as e:
        logger.error(f"Error in weekly summaries task: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@scheduler_bp.route('/market-alerts', methods=['POST'])
@require_scheduler_auth
def check_and_send_market_alerts():
    """HTTP endpoint to check for market conditions and send alerts."""
    try:
        logger.info("Starting market alerts check")
        
        # Get market data and check for alert conditions
        from web.data.stock_data import get_latest_price
        from web.data.prediction_service import get_prediction
        
        # Get current market data
        current_price = get_latest_price()
        if not current_price:
            logger.warning("Could not get current market price")
            return jsonify({"status": "warning", "message": "No market data available"})
        
        # Check for significant market movements (example: >2% change)
        price_change_percent = current_price.get('change_percent', 0)
        
        alert_conditions = []
        if abs(price_change_percent) > 2.0:
            alert_conditions.append({
                'type': 'significant_movement',
                'title': f'Significant Market Movement: {price_change_percent:+.2f}%',
                'message': f'SP500 moved {price_change_percent:+.2f}% today',
                'data': current_price
            })
        
        if not alert_conditions:
            logger.info("No market alert conditions met")
            return jsonify({"status": "success", "alerts_sent": 0})
        
        # Get users who want market alerts
        users = get_users_for_market_alerts()
        
        if not users:
            logger.info("No users found for market alerts")
            return jsonify({"status": "success", "alerts_sent": 0})
        
        email_service = EmailService(current_app)
        total_alerts_sent = 0
        
        for alert_data in alert_conditions:
            success_count = 0
            for user_id in users:
                try:
                    if email_service.send_market_alert(user_id, alert_data):
                        success_count += 1
                        logger.info(f"Market alert sent to user {user_id}")
                    time.sleep(0.5)  # Small delay
                except Exception as e:
                    logger.error(f"Error sending market alert to user {user_id}: {e}")
            
            total_alerts_sent += success_count
            logger.info(f"Market alert '{alert_data['title']}' sent to {success_count} users")
        
        return jsonify({
            "status": "success",
            "alert_conditions": len(alert_conditions),
            "users_processed": len(users),
            "alerts_sent": total_alerts_sent
        })
        
    except Exception as e:
        logger.error(f"Error in market alerts task: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@scheduler_bp.route('/cleanup-email-logs', methods=['POST'])
@require_scheduler_auth
def cleanup_old_email_logs():
    """HTTP endpoint to clean up old email logs."""
    try:
        logger.info("Starting email logs cleanup task")
        
        # Delete email logs older than 365 days
        cutoff_date = datetime.now(TZ) - timedelta(days=365)
        
        deleted_count = db.session.query(EmailLog).filter(
            EmailLog.sent_at < cutoff_date
        ).delete()
        
        db.session.commit()
        
        logger.info(f"Email logs cleanup completed: {deleted_count} logs deleted")
        
        return jsonify({
            "status": "success",
            "logs_deleted": deleted_count
        })
        
    except Exception as e:
        logger.error(f"Error in email logs cleanup task: {e}")
        db.session.rollback()
        return jsonify({"status": "error", "message": str(e)}), 500

@scheduler_bp.route('/cleanup-database', methods=['POST'])
@require_scheduler_auth
def cleanup_database():
    """HTTP endpoint to clean up old database records."""
    try:
        logger.info("Starting database cleanup task")
        
        # Get cleanup parameters from request
        data = request.get_json() or {}
        days_to_keep = data.get('days_to_keep', 90)
        
        cutoff_date = datetime.now(TZ) - timedelta(days=days_to_keep)
        
        # Clean up old articles (keep recent ones)
        from db.models import Article
        deleted_articles = db.session.query(Article).filter(
            Article.published_date < cutoff_date
        ).delete()
        
        # Clean up old email logs
        deleted_logs = db.session.query(EmailLog).filter(
            EmailLog.sent_at < cutoff_date
        ).delete()
        
        db.session.commit()
        
        logger.info(f"Database cleanup completed: {deleted_articles} articles, {deleted_logs} logs deleted")
        
        return jsonify({
            "status": "success",
            "articles_deleted": deleted_articles,
            "logs_deleted": deleted_logs,
            "cutoff_date": cutoff_date.isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in database cleanup task: {e}")
        db.session.rollback()
        return jsonify({"status": "error", "message": str(e)}), 500

@scheduler_bp.route('/targeted-emails', methods=['POST'])
@require_scheduler_auth
def send_targeted_emails():
    """HTTP endpoint to send targeted emails to specific users or groups."""
    try:
        logger.info("Starting targeted emails task")

        data = request.get_json()
        if not data:
            return jsonify({'error': 'Request body required'}), 400
        
        email_type = data.get('email_type')
        recipient_filter = data.get('recipient_filter')
        user_ids = data.get('user_ids', [])
        
        if not email_type:
            return jsonify({'error': 'Email type is required'}), 400
        if not recipient_filter:
            return jsonify({'error': 'Recipient filter is required'}), 400
        
        target_users = get_users_for_email_type(email_type, recipient_filter, user_ids)
        
        if not target_users:
            logger.info(f"No users found for filter: {recipient_filter}")
            return jsonify({"status": "success", "users_processed": 0})
        
        email_service = EmailService(current_app)
        success_count = 0
        error_count = 0
        
        # Send emails to target users
        for user_id in target_users:
            try:
                if email_service.send_targeted_emails(user_id, email_type, data):
                    success_count += 1
                    logger.info(f"Targeted email sent to user {user_id}")
                else:
                    error_count += 1
                    logger.error(f"Failed to send targeted email to user {user_id}")
                
                time.sleep(0.5)  # Small delay
                
            except Exception as e:
                error_count += 1
                logger.error(f"Error sending targeted email to user {user_id}: {e}")
        
        logger.info(f"Targeted emails task completed: {success_count} sent, {error_count} failed")
        
        return jsonify({
            "status": "success",
            "email_type": email_type,
            "recipient_filter": recipient_filter,
            "users_processed": len(target_users),
            "emails_sent": success_count,
            "errors": error_count
        })
        
    except Exception as e:
        logger.error(f"Error in targeted emails task: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500
