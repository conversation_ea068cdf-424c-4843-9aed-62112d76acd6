"""
Email scheduler for the NewsMonitor web application.

This module provides background task scheduling for sending daily summaries,
market alerts, and other automated emails.
"""

import time
from datetime import datetime, timedelta
from typing import List
from celery import Celery
from flask import current_app
from celery.schedules import crontab
from web import celery as celery_app

from web.email_service.service import TZ, EmailService
from web.models import EmailLog, User
from web.database import db

from utils.logging_config import get_web_logger
logger = get_web_logger(__name__)

@celery_app.task
def send_daily_summaries_task():
    """Celery task to send daily summaries to all subscribed users."""
    try:
        logger.info("Starting daily summaries task")

        # Get all active users who want daily summaries
        users = get_users_for_daily_summary()

        if not users:
            logger.info("No users found for daily summaries")
            return {"status": "success", "users_processed": 0}

        email_service = EmailService(current_app)
        success_count = 0
        error_count = 0

        # Generate summary data
        summary_data = email_service.generate_daily_summary_data(days_back=3)

        for user_id in users:
            try:
                if email_service.send_market_summary(user_id, summary_data):
                    success_count += 1
                    logger.info(f"Daily summary sent to user {user_id}")
                else:
                    error_count += 1
                    logger.error(
                        f"Failed to send daily summary to user {user_id}")

                # Small delay to avoid overwhelming the email server
                time.sleep(1)

            except Exception as e:
                error_count += 1
                logger.error(
                    f"Error sending daily summary to user {user_id}: {e}")

        logger.info(
            f"Daily summaries task completed: {success_count} sent, {error_count} failed")

        return {
            "status": "success",
            "users_processed": len(users),
            "emails_sent": success_count,
            "errors": error_count
        }
    except Exception as e:
        logger.error(f"Error in daily summaries task: {e}")
        return {"status": "error", "message": str(e)}

@celery_app.task
def send_weekly_summaries_task():
    """Celery task to send weekly summaries to subscribed users."""
    try:
        logger.info("Starting weekly summaries task")

        # Get all active users who want weekly summaries
        users = get_users_for_weekly_summary()

        if not users:
            logger.info("No users found for weekly summaries")
            return {"status": "success", "users_processed": 0}

        email_service = EmailService(current_app)
        success_count = 0
        error_count = 0

        # Generate summary data
        summary_data = email_service.generate_daily_summary_data(days_back=7)

        for user_id in users:
            try:
                if email_service.send_market_summary(user_id, summary_data):
                    success_count += 1
                    logger.debug(f"Weekly summary sent to user {user_id}")
                else:
                    error_count += 1
                    logger.error(
                        f"Failed to send weekly summary to user {user_id}")

                # Small delay to avoid overwhelming the email server
                time.sleep(1)

            except Exception as e:
                error_count += 1
                logger.error(
                    f"Error sending weekly summary to user {user_id}: {e}")

        logger.info(
            f"Weekly summaries task completed: {success_count} sent, {error_count} failed")

        return {
            "status": "success",
            "users_processed": len(users),
            "emails_sent": success_count,
            "errors": error_count
        }

    except Exception as e:
        logger.error(f"Error in weekly summaries task: {e}")
        return {"status": "error", "message": str(e)}

@celery_app.task
def send_market_alert_task(alert_data: dict):
    """Celery task to send market alerts to subscribed users."""
    try:
        logger.info(
            f"Starting market alert task: {alert_data.get('title', 'Unknown')}")

        # Get all active users who want market alerts
        users = get_users_for_market_alerts()

        if not users:
            logger.info("No users found for market alerts")
            return {"status": "success", "users_processed": 0}

        email_service = EmailService(current_app)
        success_count = 0
        error_count = 0

        for user_id in users:
            try:
                if email_service.send_market_alert(user_id, alert_data):
                    success_count += 1
                    logger.info(f"Market alert sent to user {user_id}")
                else:
                    error_count += 1
                    logger.error(
                        f"Failed to send market alert to user {user_id}")

                # Small delay to avoid overwhelming the email server
                time.sleep(0.5)

            except Exception as e:
                error_count += 1
                logger.error(
                    f"Error sending market alert to user {user_id}: {e}")

        logger.info(
            f"Market alert task completed: {success_count} sent, {error_count} failed")

        return {
            "status": "success",
            "users_processed": len(users),
            "emails_sent": success_count,
            "errors": error_count
        }

    except Exception as e:
        logger.error(f"Error in market alert task: {e}")
        return {"status": "error", "message": str(e)}

@celery_app.task
def cleanup_old_email_logs_task():
    """Celery task to clean up old email logs."""
    try:
        logger.info("Starting email logs cleanup task")

        # Delete email logs older than 365 days
        cutoff_date = datetime.now(TZ) - timedelta(days=365)

        deleted_count = db.session.query(EmailLog).filter(
            EmailLog.sent_at < cutoff_date
        ).delete()

        logger.info(
            f"Email logs cleanup completed: {deleted_count} logs deleted")

        return {
            "status": "success",
            "logs_deleted": deleted_count
        }

    except Exception as e:
        logger.error(f"Error in email logs cleanup task: {e}")
        return {"status": "error", "message": str(e)}


def get_users_for_daily_summary() -> List[int]:
    """Get all user IDs who should receive daily summaries."""
    try:
        user_ids = db.session.query(User.id).filter(
            User.is_active == True,
            User.is_verified == True,
            User.email_preferences['market_summary'].as_boolean() == True,
            User.email_preferences['frequency'].as_string() == 'daily'
        ).all()
        return [user_id[0] for user_id in user_ids]
    except Exception as e:
        logger.error(f"Error getting users for daily summary: {e}")
        return []


def get_users_for_weekly_summary() -> List[int]:
    """Get all user IDs who should receive weekly summaries."""
    try:
        user_ids = db.session.query(User.id).filter(
            User.is_active == True,
            User.is_verified == True,
            User.email_preferences['market_summary'].as_boolean() == True,
            User.email_preferences['frequency'].as_string() == 'weekly'
        ).all()
        return [user_id[0] for user_id in user_ids]
    except Exception as e:
        logger.error(f"Error getting users for weekly summary: {e}")
        return []


def get_users_for_market_alerts() -> List[int]:
    """Get all user IDs who should receive market alerts."""
    try:
        user_ids = db.session.query(User.id).filter(
            User.is_active == True,
            User.is_verified == True,
            User.email_preferences['market_alerts'].as_boolean() == True
        ).all()
        return [user_id[0] for user_id in user_ids]
    except Exception as e:
        logger.error(f"Error getting users for market alerts: {e}")
        return []


@celery_app.task
def send_targeted_emails_task(email_type: str, recipient_filter: str, user_ids: List[int] = None):
    """Celery task to send targeted emails to specific users or groups."""
    try:
        logger.info(f"Starting targeted emails task: {email_type} to {recipient_filter}")
        
        email_service = EmailService(current_app)
        success_count = 0
        error_count = 0
        target_users = []
        
        # Determine which users to send to based on recipient_filter
        if recipient_filter == 'selected':
            if not user_ids:
                logger.error("No user IDs provided for selected users filter")
                return {"status": "error", "message": "No user IDs provided"}
            target_users = user_ids
            
        elif recipient_filter == 'all_active':
            # Get all active users
            user_query_result = db.session.query(User.id).filter(
                User.is_active == True
            ).all()
            target_users = [user_id[0] for user_id in user_query_result]
            
        elif recipient_filter == 'all_subscribed':
            # Get all users subscribed to the specific email type
            if email_type == 'daily_summary':
                target_users = get_users_for_daily_summary()
            elif email_type == 'market_alert':
                target_users = get_users_for_market_alerts()
            else:
                # For other email types, use all active users who have market_summary enabled
                user_query_result = db.session.query(User.id).filter(
                    User.is_active == True,
                    User.is_verified == True,
                    User.email_preferences['market_summary'].as_boolean() == True
                ).all()
                target_users = [user_id[0] for user_id in user_query_result]
                
        elif recipient_filter == 'admins_only':
            # Get all admin users
            user_query_result = db.session.query(User.id).filter(
                User.is_active == True,
                User.is_admin == True
            ).all()
            target_users = [user_id[0] for user_id in user_query_result]
            
        else:
            logger.error(f"Unknown recipient filter: {recipient_filter}")
            return {"status": "error", "message": f"Unknown recipient filter: {recipient_filter}"}
        
        if not target_users:
            logger.info(f"No users found for filter: {recipient_filter}")
            return {"status": "success", "users_processed": 0, "message": "No users found for the specified filter"}
        
        logger.info(f"Sending {email_type} emails to {len(target_users)} users")
        
        # Send emails based on type
        for user_id in target_users:
            try:
                success = False
                
                if email_type == 'daily_summary':
                    # Generate summary data for daily summary
                    summary_data = email_service.generate_daily_summary_data(days_back=3)
                    success = email_service.send_market_summary(user_id, summary_data, allow_resend=True)
                    
                elif email_type == 'market_alert':
                    # For market alerts, we'll send a generic alert or use recent data
                    alert_data = {
                        'title': 'Market Alert from Admin',
                        'message': 'This is a market alert sent by the administrator.',
                        'timestamp': datetime.now(tz=TZ)
                    }
                    success = email_service.send_market_alert(user_id, alert_data)
                    
                elif email_type == 'test_email':
                    # Send a test email
                    user = db.session.query(User).filter(User.id == user_id).first()
                    if user:
                        success = email_service.send_email(
                            recipient=user.email,
                            subject='Test Email from NewsMonitor Admin',
                            html_body=f'<html><body><h2>Test Email</h2><p>Hello {user.first_name or user.username},</p><p>This is a test email sent by the administrator to verify the email system is working correctly.</p><p>Best regards,<br>NewsMonitor Team</p></body></html>',
                            text_body=f'Hello {user.first_name or user.username},\n\nThis is a test email sent by the administrator to verify the email system is working correctly.\n\nBest regards,\nNewsMonitor Team',
                            user_id=user_id,
                            email_type='test_email'
                        )
                else:
                    logger.error(f"Unknown email type: {email_type}")
                    error_count += 1
                    continue
                
                if success:
                    success_count += 1
                    logger.debug(f"{email_type} sent to user {user_id}")
                else:
                    error_count += 1
                    logger.error(f"Failed to send {email_type} to user {user_id}")
                
                # Small delay to avoid overwhelming the email server
                time.sleep(0.5)
                
            except Exception as e:
                error_count += 1
                logger.error(f"Error sending {email_type} to user {user_id}: {e}")
        
        logger.info(f"Targeted emails task completed: {success_count} sent, {error_count} failed")
        
        return {
            "status": "success",
            "users_processed": len(target_users),
            "emails_sent": success_count,
            "errors": error_count,
            "email_type": email_type,
            "recipient_filter": recipient_filter
        }
        
    except Exception as e:
        logger.error(f"Error in targeted emails task: {e}")
        return {"status": "error", "message": str(e)}
