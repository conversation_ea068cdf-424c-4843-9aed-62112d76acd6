"""
Email service for the NewsMonitor web application.

This module provides email functionality including daily summaries,
market alerts, and user notifications.
"""

from datetime import datetime, timedelta
import traceback
from typing import List, Dict, Any, Optional
from zoneinfo import ZoneInfo
from flask import current_app, render_template
from flask_mail import Mail, Message

from web.config import TZ
from web.data.stock_data import get_stock_data
from web.data.prediction_service import get_prediction, get_llm_prediction
from web.database import db
from web.models import EmailLog, User


from utils.logging_config import get_web_logger
logger = get_web_logger(__name__)

class EmailService:
    """Email service for sending various types of emails."""

    def __init__(self, app=None):
        self.app = app
        self.mail = None
        if app:
            self.init_app(app)
        else:
            logger.warning("Email service initialized without app")

    def init_app(self, app):
        """Initialize the email service with Flask app."""
        self.app = app
        self.mail = Mail(app)
        logger.info("Email service initialized with app")

    def send_email(self, recipient: str, subject: str, html_body: str, text_body: str = None, user_id: int = None, email_type: str = 'general') -> bool:
        """Send an email and log the result."""
        try:
            msg = Message(
                subject=subject,
                recipients=[recipient],
                html=html_body,
                body=text_body or self._html_to_text(html_body)
            )
            logger.info(f"Sending email to {recipient}: {subject}")

            self.mail.send(msg)

            # Log successful email
            self._log_email(user_id, email_type, subject, recipient, 'sent')
            return True

        except Exception as e:
            # Log failed email
            self._log_email(user_id, email_type, subject,
                            recipient, 'failed', str(e))
            return False

    def send_market_summary(self, user_or_id, summary_data: Dict[str, Any], allow_resend: bool = False) -> bool:
        """Send market summary to a user."""
        try:
            # Handle both User objects and user IDs

            if isinstance(user_or_id, User):
                user_id = user_or_id.id
            else:
                user_id = user_or_id

            # Get user data from email service
            user_data = db.session.query(User).filter(
                User.id == user_id).first()
            if not user_data:
                logger.error(f"User {user_id} not found")
                return False

            user_email = user_data.email
            user_preferences = user_data.email_preferences

            # Check if user wants market summaries
            if not user_preferences.get('market_summary', True):
                return False

            # Check if we already sent a summary today
            if not allow_resend and self._already_sent_today(user_id, 'market_summary'):
                logger.info(
                    f"Market summary already sent to user {user_id} today")
                return False

            # Render email templates
            html_body = render_template('email/daily_summary.html',
                                        user=user_data,
                                        data=summary_data,
                                        year=datetime.now().year)

            text_body = render_template('email/daily_summary.txt',
                                        user=user_data,
                                        data=summary_data,
                                        year=datetime.now().year)

            subject = f"Daily Market Summary - {datetime.now().strftime('%B %d, %Y')}"

            res = self.send_email(
                recipient=user_email,
                subject=subject,
                html_body=html_body,
                text_body=text_body,
                user_id=user_id,
                email_type='market_summary'
            )

            return res

        except Exception as e:
            logger.error(
                f"Error sending market summary to user {user_id if 'user_id' in locals() else 'unknown'}: {e}")
            import traceback
            print(traceback.format_exc())
            return False

    def send_market_alert(self, user_or_id, alert_data: Dict[str, Any]) -> bool:
        """Send market alert to a user."""
        try:

            if isinstance(user_or_id, User):
                user_id = user_or_id.id
                user_email = user_or_id.email
                user_preferences = user_or_id.email_preferences
                user_obj = user_or_id
            else:
                # Query user from database in our own session
                user_id = user_or_id
                user = db.session.query(User).filter(
                    User.id == user_id).first()
                if not user:
                    logger.error(f"User {user_id} not found")
                    return False
                user_email = user.email
                user_preferences = user.email_preferences

            # Check if user wants market alerts
            if not user_preferences.get('market_alerts', True):
                return True

            # Render email templates
            html_body = render_template('email/market_alert.html',
                                        user=user_obj,
                                        alert=alert_data)
            text_body = render_template('email/market_alert.txt',
                                        user=user_obj,
                                        alert=alert_data)

            subject = f"Market Alert: {alert_data.get('title', 'Significant Market Movement')}"

            return self.send_email(
                recipient=user_email,
                subject=subject,
                html_body=html_body,
                text_body=text_body,
                user_id=user_id,
                email_type='market_alert'
            )

        except Exception as e:
            logger.error(
                f"Error sending market alert to user {user_id if 'user_id' in locals() else 'unknown'}: {e}")
            return False


    def send_verification_email(self, user: User, token: str) -> bool:
        """Send email verification email."""
        try:
            verification_url = f"{current_app.config.get('BASE_URL', 'http://localhost:5000')}/auth/verify-email/{token}"

            html_body = render_template('email/verify_email.html',
                                        user=user,
                                        verification_url=verification_url)
            text_body = render_template('email/verify_email.txt',
                                        user=user,
                                        verification_url=verification_url)

            subject = "Verify Your Email Address - Market Monitor"

            return self.send_email(
                recipient=user.email,
                subject=subject,
                html_body=html_body,
                text_body=text_body,
                user_id=user.id,
                email_type='verification'
            )

        except Exception as e:
            logger.error(
                f"Error sending verification email to user {user.id}: {e}")
            return False

    def send_password_reset_email(self, user: User, token: str) -> bool:
        """Send password reset email."""
        try:
            logger.info(f"Sending password reset email to user {user.id}")
            reset_url = f"{current_app.config.get('BASE_URL', 'http://localhost:5000')}/auth/reset-password/{token}"

            html_body = render_template('email/password_reset.html',
                                        user=user,
                                        reset_url=reset_url,
                                        year=datetime.now(TZ).year)
            text_body = render_template('email/password_reset.txt',
                                        user=user,
                                        reset_url=reset_url,
                                        year=datetime.now(TZ).year)

            subject = "Password Reset Request - Market Monitor"

            return self.send_email(
                recipient=user.email,
                subject=subject,
                html_body=html_body,
                text_body=text_body,
                user_id=user.id,
                email_type='password_reset'
            )

        except Exception as e:
            logger.error(
                f"Error sending password reset email to user {user.id}: {e}")
            return False

    def generate_daily_summary_data(self, days_back: int = 7) -> Dict[str, Any]:
        """Generate data for daily summary email."""
        try:
            # Get market data
            now = datetime.now(TZ)
            end_date = now.strftime('%Y-%m-%d')
            start_date = (now - timedelta(days=days_back)).strftime('%Y-%m-%d')

            market_data = get_stock_data("SPY", start_date, end_date)

            # Get predictions
            try:
                prediction_data = get_prediction()
            except Exception as e:
                logger.warning(f"Could not get prediction data: {e}")
                prediction_data = None

            # Get LLM prediction
            try:
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                llm_prediction = loop.run_until_complete(get_llm_prediction(days_back=days_back))
            except Exception as e:
                logger.warning(f"Could not get LLM prediction: {e}")
                llm_prediction = None

            return {
                'date': now,
                'market_data': None,  # market_data is incorrect
                'news': None,
                'prediction': None,  # prediction_data is incorrect
                'llm_prediction': llm_prediction
            }

        except Exception as e:
            logger.error(f"Error generating daily summary data: {e}")
            return {
                'date': datetime.now(TZ),
                'error': str(e)
            }

    def _log_email(self, user_id: int, email_type: str, subject: str, recipient: str, status: str, error_message: str = None):
        """Log email sending attempt."""
        try:
            email_log = EmailLog(
                user_id=user_id,
                email_type=email_type,
                subject=subject,
                recipient_email=recipient,
                status=status,
                sent_at=datetime.now(TZ),
                error_message=error_message
            )
            db.session.add(email_log)
            db.session.commit()

            return email_log
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error logging email: {e}")
            return None

    def _already_sent_today(self, user_id: int, email_type: str) -> bool:
        """Check if we already sent this type of email today."""
        try:
            return db.session.query(EmailLog).filter(
                EmailLog.user_id == user_id,
                EmailLog.email_type == email_type,
                EmailLog.status == 'sent',
                EmailLog.sent_at >= datetime.now(TZ).replace(
                    hour=0, minute=0, second=0, microsecond=0)
            ).first() is not None
        except Exception as e:
            logger.error(f"Error checking if email already sent: {e}")
            return False

    def _html_to_text(self, html: str) -> str:
        """Convert HTML to plain text."""
        # Simple HTML to text conversion
        import re
        text = re.sub('<[^<]+?>', '', html)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
