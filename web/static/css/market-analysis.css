/**
 * Market Analysis Page Styles
 * Comprehensive styling for the Market Analysis tab
 */

.market-analysis-container {
    padding: 20px 0;
    background: #f8fafc;
    min-height: calc(100vh - 200px);
}

/* Ticker Selector Styles */
.ticker-selector .card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
}

.ticker-selector .card-title {
    color: #1f2937;
    font-weight: 700;
    margin-bottom: 15px;
}

.ticker-selector .form-select {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 10px 15px;
    font-weight: 500;
    transition: border-color 0.3s ease;
}

.ticker-selector .form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

#load-analysis-btn {
    padding: 10px 20px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

#load-analysis-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Chart Container Styles */
.chart-container {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 25px;
    border: 1px solid #e5e7eb;
}

/* Time Range Selector Styles */
.time-range-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.time-range-btn {
    padding: 4px 8px;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    background: white;
    color: #6b7280;
    font-weight: 600;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 60px;
}

.time-range-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    background: #f8fafc;
}

.time-range-btn.active {
    border-color: #3b82f6;
    background: #3b82f6;
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.time-range-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.chart-container h5 {
    color: #1f2937;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

#price-chart {
    border-radius: 8px;
    overflow: hidden;
}

/* AI Analysis Section Styles */
.ai-analysis-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
}

.prediction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.prediction-header h5 {
    color: #1f2937;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* API Provider Selection */
.btn-group .btn-outline-primary {
    border-color: #d1d5db;
    color: #6b7280;
    font-weight: 600;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.btn-group .btn-outline-primary:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
    color: #374151;
}

.btn-group .btn-check:checked + .btn-outline-primary {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

/* Confidence Display */
.confidence-display {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border: 1px solid #bae6fd;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
}

.confidence-display h6 {
    color: #0c4a6e;
    font-weight: 700;
    margin-bottom: 10px;
}

.confidence-display h4 {
    margin: 0;
    font-weight: 800;
}

.confidence-display .progress {
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.7);
}

.confidence-display .progress-bar {
    border-radius: 12px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Evidence Container */
.evidence-container {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
}

.evidence-container h6 {
    color: #1e293b;
    font-weight: 700;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.evidence-item {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    border-left: 4px solid #3b82f6;
}

.evidence-item:last-child {
    margin-bottom: 0;
}

.evidence-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.evidence-fact {
    color: #000000 !important;
    font-weight: 600 !important;
    line-height: 1.5;
    margin-bottom: 8px;
    background: #ffffff !important;
    padding: 8px !important;
    border: 1px solid #333333 !important;
    border-radius: 4px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.article-reference {
    font-size: 0.85rem;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 6px;
}

.article-reference a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.article-reference a:hover {
    color: #2563eb;
    text-decoration: underline;
}

/* Themes Section */
.themes-section {
    background: linear-gradient(135deg, #fffbeb, #fef3c7);
    border: 1px solid #fbbf24;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
}

.themes-section h6 {
    color: #92400e;
    font-weight: 700;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.themes-section .theme-text {
    color: #78350f;
    font-weight: 500;
    line-height: 1.6;
    margin-bottom: 15px;
}

.theme-supporting-articles {
    border-top: 1px solid #fbbf24;
    padding-top: 15px;
}

.supporting-articles-title {
    color: #92400e;
    font-weight: 700;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.supporting-article-item {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 8px;
    font-size: 0.85rem;
    color: #78350f;
    display: flex;
    align-items: center;
    gap: 6px;
}

.supporting-article-item:last-child {
    margin-bottom: 0;
}

.supporting-article-item a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
}

.supporting-article-item a:hover {
    color: #2563eb;
    text-decoration: underline;
}

/* Technical Indicators */
.technical-indicators {
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
    border: 1px solid #60a5fa;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
}

.technical-indicators h6 {
    color: #1e40af;
    font-weight: 700;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.technical-indicators .row > div {
    margin-bottom: 8px;
}

.technical-indicators strong {
    color: #1e40af;
}

/* Detailed Outlook Section */
.detailed-outlook-section {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    border: 1px solid #86efac;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
}

.detailed-outlook-section h6 {
    color: #166534;
    font-weight: 700;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detailed-outlook-section .evidence-item {
    background: white;
    border: 1px solid #d1fae5;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    border-left: 3px solid #10b981;
}

.detailed-outlook-section .evidence-fact {
    color: #000000 !important;
    font-weight: 600 !important;
    line-height: 1.5;
    margin-bottom: 6px;
    background: #ffffff !important;
    padding: 8px !important;
    border: 1px solid #333333 !important;
    border-radius: 4px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.detailed-outlook-section .article-reference {
    font-size: 0.8rem;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 4px;
}

.outlook-evidence-container {
    max-height: none;
    overflow: visible;
}

.outlook-evidence-container .evidence-item:last-child {
    margin-bottom: 0;
}

.outlook-content-full {
    background: white;
    border: 1px solid #d1fae5;
    border-radius: 8px;
    padding: 15px;
    white-space: pre-wrap;
    line-height: 1.6;
    color: #374151;
    max-height: none;
    overflow: visible;
}

/* Probability Cards */
.card.text-center {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.card.text-center:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card.text-center .card-body {
    padding: 15px 10px;
}

.card.text-center .card-title {
    font-size: 0.9rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.card.text-center h5 {
    font-weight: 800;
    font-size: 1.2rem;
}

/* Loading States */
.loading-spinner {
    text-align: center;
    padding: 40px;
    color: #6b7280;
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
    margin-bottom: 15px;
}

.loading-spinner p {
    font-weight: 500;
    margin: 0;
}

/* Error States */
.error-message {
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    border: 1px solid #fca5a5;
    color: #991b1b;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.error-message i {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.error-message strong {
    font-weight: 700;
}

/* Market Overview Section */
.market-overview-section {
    margin-bottom: 24px;
}

.market-overview-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.market-overview-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.market-overview-title {
    color: #1e40af;
    font-weight: 700;
    font-size: 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    letter-spacing: 0.5px;
}

.market-overview-content {
    display: grid;
    gap: 24px;
    align-items: start;
}

/* Prediction Section */
.prediction-section {
    padding: 16px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 10px;
    border: 1px solid #bae6fd;
}

.prediction-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.prediction-label {
    font-size: 12px;
    font-weight: 600;
    color: #0369a1;
    letter-spacing: 0.5px;
}

.prediction-value {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 6px;
    line-height: 1;
}

.prediction-confidence {
    font-size: 11px;
    color: #64748b;
    font-weight: 500;
    margin-bottom: 10px;
}

.prediction-bar {
    background: #e2e8f0;
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
}

.prediction-bar-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.6s ease;
}

/* Levels Section */
.levels-section {
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 10px;
    border: 1px solid #cbd5e1;
}

.levels-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.levels-label {
    font-size: 12px;
    font-weight: 600;
    color: #0369a1;
    letter-spacing: 0.5px;
}

.levels-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.level-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #e2e8f0;
}

.level-item:last-child {
    border-bottom: none;
}

.level-info {
    display: flex;
    align-items: center;
    gap: 6px;
}

.level-name {
    font-size: 14px;
    font-weight: 500;
    color: #475569;
    text-transform: capitalize;
}

.level-value {
    font-size: 12px;
    font-weight: 700;
}

/* Trigger Display Component */
.trigger-display {
    padding: 14px;
    background: linear-gradient(135deg, #faf5df 0%, #fbf9e8 100%);
    border: 1px solid #d1d5db;
    border-left: 3px solid #f3bc5c;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.trigger-display:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    border-left-color: #d97706;
}

.trigger-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.trigger-icon {
    width: 20px;
    height: 20px;
    background: #f59e0b;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 11px;
}

.trigger-label {
    font-size: 10px;
    font-weight: 600;
    color: #78350f;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.trigger-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 10px;
}

.trigger-text {
    flex: 1;
    color: #374151;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.4;
    background: rgba(255, 255, 255, 0.6);
    padding: 8px 10px;
    border-radius: 6px;
    border: 1px solid rgba(209, 213, 219, 0.5);
    word-wrap: break-word;
}

.trigger-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
}

.trigger-pulse {
    width: 8px;
    height: 8px;
    background: #f59e0b;
    border-radius: 50%;
    animation: gentle-pulse 2s ease-in-out infinite;
}

@keyframes gentle-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.trigger-status {
    font-size: 9px;
    font-weight: 500;
    color: #6b7280;
    letter-spacing: 0.3px;
    text-transform: uppercase;
}

/* Responsive Design */
@media (max-width: 768px) {
    .market-analysis-container {
        padding: 15px 0;
    }

    .chart-container,
    .ai-analysis-section {
        padding: 20px 15px;
    }

    .prediction-header {
        align-items: stretch;
    }

    .btn-group {
        width: 100%;
    }

    .btn-group .btn {
        flex: 1;
    }

    .confidence-display .row {
        flex-direction: column;
    }

    .confidence-display .col-md-6 {
        margin-bottom: 15px;
    }

    .confidence-display .col-md-6:last-child {
        margin-bottom: 0;
    }

    /* Compact Market Overview Mobile */
    .market-overview-content {
        gap: 16px;
    }

    .market-overview-card {
        padding: 16px;
    }

    .market-overview-title {
        font-size: 14px;
        margin-bottom: 12px;
    }

    .prediction-section,
    .levels-section {
        padding: 12px;
    }

    .prediction-value {
        font-size: 18px;
    }

    .level-name {
        font-size: 10px;
    }

    .level-value {
        font-size: 11px;
    }
    
    /* Time Range Selector Mobile */
    .time-range-buttons {
        gap: 6px;
        margin-bottom: 15px;
    }
    
    .time-range-btn {
        padding: 4px 8px;
        font-size: 0.75rem;
        min-width: 50px;
    }
    
    /* Trigger Display Mobile Responsive */
    .trigger-display {
        padding: 10px;
    }
    
    .trigger-content {
        flex-direction: column;
        gap: 8px;
    }
    
    .trigger-text {
        font-size: 11px;
        padding: 6px 8px;
    }
    
    .trigger-header {
        margin-bottom: 6px;
    }
    
    .trigger-icon {
        width: 18px;
        height: 18px;
        font-size: 10px;
    }
    
    .trigger-indicator {
        flex-direction: row;
        align-self: flex-start;
        gap: 6px;
    }
}

@media (max-width: 576px) {
    .chart-container,
    .ai-analysis-section {
        padding: 15px;
        margin-left: -5px;
        margin-right: -5px;
    }
    
    .evidence-item {
        padding: 12px;
    }
    
    .themes-section,
    .technical-indicators {
        padding: 15px;
    }
    
    #price-chart {
        height: 300px !important;
    }
}

/* Animation for smooth transitions */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
