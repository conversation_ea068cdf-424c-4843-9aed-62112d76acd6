/**
 * Market Overview JavaScript Module
 * Handles the market overview banner with real-time price data
 */

class MarketOverview {
    constructor() {
        this.tickers = ['SPY', 'VOO', '^GSPC', '^DJI', '^IXIC'];
        this.elementPrefixes = {
            'SPY': 'spy',
            'VOO': 'voo',
            '^GSPC': 'sp500',
            '^DJI': 'dow',
            '^IXIC': 'nasdaq'
        };
        this.priceData = {};
        this.updateInterval = null;
        this.marketStatusInterval = null;
        
        this.init();
    }

    init() {
        this.loadMarketData();
        this.setupRealTimeUpdates();
    }

    async loadMarketData() {
        try {        
            const params = new URLSearchParams();
            this.tickers.forEach(ticker => params.append('tickers', ticker));
            const response = await fetch(`/api/market-overview?${params.toString()}`);
            const data = await response.json();
            if (data.error) {
                throw new Error(data.error);
            }
            this.priceData = data;
            this.updateDisplay();
        } catch (error) {
            console.error('Error loading market data:', error.message);
            this.showError();
        }
    }

    updateDisplay() {
        this.tickers.forEach(ticker => {
            const elementPrefix = this.elementPrefixes[ticker];
            this.updateMetric(elementPrefix, ticker);
        });
    }

    updateMetric(elementPrefix, dataKey) {
        const data = this.priceData[dataKey];
        if (!data) return;

        const priceElement = document.getElementById(`${elementPrefix}-price`);
        const changeElement = document.getElementById(`${elementPrefix}-change`);

        if (priceElement) {
            if (dataKey.startsWith('^')) {
                // Index values (no dollar sign, show as whole numbers)
                priceElement.textContent = data.price.toFixed(0);
            } else {
                // ETF prices (show with dollar sign and 2 decimals)
                priceElement.textContent = `$${data.price.toFixed(2)}`;
            }
        }

        if (changeElement) {
            const changeText = `${data.change >= 0 ? '+' : ''}${data.change.toFixed(2)} (${data.changePercent >= 0 ? '+' : ''}${data.changePercent.toFixed(2)}%)`;
            changeElement.textContent = changeText;

            // Update color based on change
            changeElement.className = `metric-change ${data.change >= 0 ? 'positive' : 'negative'}`;
        }
    }

    showError() {
        // Show error state for all metrics
        const metrics = ['sp500', 'dow', 'nasdaq', 'spy', 'voo'];
        
        metrics.forEach(metric => {
            const priceElement = document.getElementById(`${metric}-price`);
            const changeElement = document.getElementById(`${metric}-change`);
            
            if (priceElement) priceElement.textContent = 'Error';
            if (changeElement) {
                changeElement.textContent = 'Unable to load';
                changeElement.className = 'metric-change';
            }
        });
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        if (this.marketStatusInterval) {
            clearInterval(this.marketStatusInterval);
        }
    }
            
    /**
     * Format time duration in a human-readable format
     * @param {number} milliseconds
     * @returns {string} Formatted time string
     */
    formatTimeUntil(milliseconds) {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        
        if (hours > 24) {
            const days = Math.floor(hours / 24);
            const remainingHours = hours % 24;
            return `${days}d ${remainingHours}h ${minutes}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    }
    
    /**
     * Setup real-time updates for the price chart
     */
    setupRealTimeUpdates() {
        const updateMarketStatus = async () => {
            try {
                const response = await fetch('/api/is-market-open');
                const data = await response.json();
                if (data.error) {
                    console.error('Error checking market hours:', data.error);
                    return;
                }
                const isOpen = data.isOpen;
                if (isOpen) {
                    console.log('Market is open - updating every 10 seconds');
                    // Update every 10 seconds during market hours
                    this.updateInterval = setInterval(() => {
                        this.loadMarketData();
                    }, 10 * 1000);
                } else {
                    console.log('Market is closed - updating every 6 hours');
                    // Update every 6 hours when market is closed
                    this.updateInterval = setInterval(() => {
                        this.loadMarketData();
                    }, 6 * 3600 * 1000);
                }
            } catch (error) {
                console.error('Error checking market hours:', error);
            }
        };
        
        // Initial check
        updateMarketStatus();
        
        // Check market status every hour
        this.marketStatusInterval = setInterval(updateMarketStatus, 3600 * 1000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.marketOverview = new MarketOverview();
});

// Clean up on page unload
window.addEventListener('beforeunload', function() {
    if (window.marketOverview) {
        window.marketOverview.destroy();
    }
});
