/**
 * News JavaScript Module
 * Handles news display and filtering
 */

// Configuration Constants for News Module
const NEWS_CONFIG = {
    API_ENDPOINTS: {
        FEATURED: '/api/featured-news',
        MORE_NEWS: '/api/news',
        SEARCH: '/api/search-news',
        IS_MARKET_OPEN: '/api/is-market-open'
    },

    PAGINATION: {
        MORE_ARTICLES_LIMIT: 10,
        INITIAL_OFFSET: 0
    },

    TIMEOUTS: {
        SEARCH_DEBOUNCE: 300,
        API_TIMEOUT: 10000
    },

    DOM_IDS: {
        FEATURED_GRID: 'featured-news-grid',
        MORE_NEWS_LIST: 'more-news-list',
        SEARCH_INPUT: 'news-search-input',
        ADVANCED_FILTERS: 'advanced-filters',
        DATE_FROM: 'date-from',
        DATE_TO: 'date-to',
        NEWS_SOURCE: 'news-source',
        SENTIMENT_FILTER: 'sentiment-filter'
    },

    CSS_CLASSES: {
        LOADING: 'news-loading',
        ERROR: 'news-error',
        FEATURED_ARTICLE: 'featured-article',
        NEWS_LIST_ITEM: 'news-list-item'
    }
};

import { debounce, escapeHtml, formatDateWithTime, isMobile, sanitizeErrorMessage } from './utils.js';

class News {
    constructor() {
        // Article data
        this.featuredArticles = [];
        this.moreArticles = [];
        this.displayedMoreArticles = [];

        // Pagination and loading state
        this.moreArticlesOffset = NEWS_CONFIG.PAGINATION.INITIAL_OFFSET;
        this.moreArticlesLimit = NEWS_CONFIG.PAGINATION.MORE_ARTICLES_LIMIT;
        this.isLoading = false;
        this.abortController = null;
        
        // Search state
        this.isSearchActive = false;
        this.searchOffset = 0;
        this.searchArticles = [];

        // Filter state
        this.currentFilters = {
            search: '',
            dateFrom: '',
            dateTo: '',
            source: '',
            sentiment: ''
        };

        // Debounced search function
        this.debouncedSearch = debounce(
            this.performSearch.bind(this),
            NEWS_CONFIG.TIMEOUTS.SEARCH_DEBOUNCE
        );

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupScrollListener();
        this.loadNews();
    }

    setupEventListeners() {
        try {
            // Search functionality with debouncing
            const searchInput = document.getElementById(NEWS_CONFIG.DOM_IDS.SEARCH_INPUT);
            const searchButton = document.getElementById('search-button');

            if (searchInput) {
                searchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.performSearch();
                    }
                });

                // Add real-time search with debouncing
                searchInput.addEventListener('input', (e) => {
                    if (e.target.value.trim() !== this.currentFilters.search) {
                        this.debouncedSearch();
                    }
                });
            }

            if (searchButton) {
                searchButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.performSearch();
                });
            }

        // Advanced search toggle
        const toggleAdvanced = document.getElementById('toggle-advanced-search');
        const advancedFilters = document.getElementById('advanced-filters');
        
        if (toggleAdvanced && advancedFilters) {
            toggleAdvanced.addEventListener('click', (e) => {
                e.preventDefault();
                advancedFilters.classList.toggle('show');
                const icon = toggleAdvanced.querySelector('i');
                if (icon) {
                    if (advancedFilters.classList.contains('show')) {
                        icon.className = 'bi bi-chevron-up';
                        toggleAdvanced.setAttribute('aria-expanded', 'true');
                    } else {
                        icon.className = 'bi bi-sliders';
                        toggleAdvanced.setAttribute('aria-expanded', 'false');
                    }
                }
            });
        }

        // Filter controls
        const applyFiltersBtn = document.getElementById('apply-filters');
        const clearFiltersBtn = document.getElementById('clear-filters');
        
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.applyFilters();
            });
        }
        
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.clearFilters();
            });
        }
        
            // Handle page visibility changes
            document.addEventListener('visibilitychange', () => {
                if (document.hidden && this.abortController) {
                    this.abortController.abort();
                }
            });
        } catch (error) {
            console.error('Error setting up event listeners:', error);
        }
    }

    async loadNews() {
        if (this.isLoading) {
            console.log('News loading already in progress');
            return;
        }
        
        try {
            this.isLoading = true;
            this.abortController = new AbortController();
            this.showLoading();

            // Load featured news and more news separately with timeout
            await Promise.allSettled([
                this.loadFeaturedNews(),
                this.loadMoreNews()
            ]);

        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('Error loading news:', error);
                this.showError(sanitizeErrorMessage(error.message));
            }
        } finally {
            this.isLoading = false;
            this.abortController = null;
        }
    }

    async loadFeaturedNews() {
        try {
            const response = await fetch(`${NEWS_CONFIG.API_ENDPOINTS.FEATURED}?limit=6`, {
                signal: this.abortController?.signal,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error('Featured news endpoint not found');
                } else if (response.status >= 500) {
                    throw new Error('Server error occurred');
                } else {
                    const data = await response.json().catch(() => ({}));
                    throw new Error(data.error || `Request failed with status ${response.status}`);
                }
            }
            
            const data = await response.json();
            this.featuredArticles = Array.isArray(data.all) ? data.all : [];
            this.renderFeaturedNews();

        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('Featured news request aborted');
                return;
            }
            
            console.error('Error loading featured news:', error);
            
            // Fallback to regular news API
            try {
                const fallbackResponse = await fetch('/api/news?limit=6', {
                    signal: this.abortController?.signal,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (fallbackResponse.ok) {
                    const fallbackData = await fallbackResponse.json();
                    this.featuredArticles = Array.isArray(fallbackData.all) ? fallbackData.all : [];
                    this.renderFeaturedNews();
                } else {
                    throw new Error('Fallback request also failed');
                }
            } catch (fallbackError) {
                if (fallbackError.name !== 'AbortError') {
                    this.showFeaturedError(sanitizeErrorMessage(error.message));
                }
            }
        }
    }

    async loadMoreNews() {
        try {
            // Get IDs of featured articles to exclude
            const excludeIds = this.featuredArticles
                .filter(article => article && article.id)
                .map(article => article.id);
                
            const params = new URLSearchParams({
                limit: '20',
                offset: '0'
            });

            // Add exclude IDs as separate parameters
            excludeIds.forEach(id => {
                if (id != null) {
                    params.append('exclude_ids', String(id));
                }
            });

            const response = await fetch(`${NEWS_CONFIG.API_ENDPOINTS.MORE_NEWS}?${params.toString()}`, {
                signal: this.abortController?.signal,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error('More news endpoint not found');
                } else if (response.status >= 500) {
                    throw new Error('Server error occurred');
                } else {
                    const data = await response.json().catch(() => ({}));
                    throw new Error(data.error || `Request failed with status ${response.status}`);
                }
            }
            
            const data = await response.json();
            this.moreArticles = Array.isArray(data.all) ? data.all : [];
            this.renderMoreNews();

        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('More news request aborted');
                return;
            }
            
            console.error('Error loading more news:', error);
            
            // Fallback to regular news API
            try {
                const fallbackResponse = await fetch('/api/news?limit=20', {
                    signal: this.abortController?.signal,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (fallbackResponse.ok) {
                    const fallbackData = await fallbackResponse.json();
                    this.moreArticles = Array.isArray(fallbackData.all) ? fallbackData.all : [];
                    this.renderMoreNews();
                } else {
                    throw new Error('Fallback request also failed');
                }
            } catch (fallbackError) {
                if (fallbackError.name !== 'AbortError') {
                    this.showMoreNewsError(sanitizeErrorMessage(error.message));
                }
            }
        }
    }

    renderFeaturedNews() {
        const container = document.getElementById('featured-news-grid');

        if (!container) {
            console.warn('Featured news grid container not found');
            return;
        }

        if (!Array.isArray(this.featuredArticles) || this.featuredArticles.length === 0) {
            container.innerHTML = '<div class="news-error">No featured news available</div>';
            return;
        }

        try {
            // Take up to 6 articles for the 2x3 grid
            const gridArticles = this.featuredArticles.slice(0, 6).filter(Boolean);
            
            const articlesHtml = gridArticles.map(article => {
                const sentiment = this.getSentimentFromArticle(article);
                const sentimentClass = sentiment ? `sentiment-${sentiment.toLowerCase()}` : '';
                const sentimentBadge = (sentiment && window.isAdmin) ? `<span class="sentiment-badge ${sentimentClass}">${escapeHtml(sentiment)}</span>` : '';

                return this.createArticleHtml({
                    article,
                    className: 'featured-article',
                    sentimentBadge,
                    contentLength: 120,
                    showInfluence: window.isAdmin
                });
            }).join('');

            container.innerHTML = articlesHtml;
        } catch (error) {
            console.error('Error rendering featured articles:', error);
            container.innerHTML = '<div class="news-error">Error displaying featured articles</div>';
        }
    }

    renderMoreNews() {
        const container = document.getElementById('more-news-list');
        if (!container) return;

        if (this.moreArticles.length === 0) {
            container.innerHTML = '<div class="news-error">No more news available</div>';
            return;
        }

        // Load initial batch
        this.loadMoreArticles(true);
    }

    async loadMoreArticles(reset = false) {
        const container = document.getElementById('more-news-list');
        if (!container) {
            console.warn('More news container not found');
            return;
        }

        if (reset) {
            this.moreArticlesOffset = 0;
            this.displayedMoreArticles = [];
            container.innerHTML = '';
        }

        // If this is not a reset and we're loading more, show loading spinner
        if (!reset) {
            this.showPaginationLoading();
        }

        try {
            // Get IDs of featured articles to exclude
            const excludeIds = this.featuredArticles
                .filter(article => article && article.id)
                .map(article => article.id);

            const params = new URLSearchParams({
                limit: reset ? '20' : '10',
                offset: this.moreArticlesOffset.toString()
            });

            // Add exclude IDs as separate parameters
            excludeIds.forEach(id => {
                if (id != null) {
                    params.append('exclude_ids', String(id));
                }
            });

            const response = await fetch(`${NEWS_CONFIG.API_ENDPOINTS.MORE_NEWS}?${params.toString()}`, {
                signal: this.abortController?.signal,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to load more articles');
            }

            const data = await response.json();
            const newArticles = Array.isArray(data.all) ? data.all : [];

            if (newArticles.length === 0 && this.displayedMoreArticles.length === 0) {
                container.innerHTML = '<div class="news-error">No more news available</div>';
                return;
            }

            // Add articles to displayed list
            this.displayedMoreArticles.push(...newArticles);
            this.moreArticlesOffset += newArticles.length;

            this.renderMoreArticlesList();

        } catch (error) {
            console.error('Error loading more articles:', error);
            this.hidePaginationLoading();
            if (!reset) {
                this.showPaginationError();
            }
        }
    }

    renderMoreArticlesList() {
        const container = document.getElementById('more-news-list');
        if (!container) return;

        this.hidePaginationLoading();

        try {
            console.log(`Rendering ${this.displayedMoreArticles.length} more articles`);
            // Render articles
            const articlesHtml = this.displayedMoreArticles.map(article => {
                const imageHtml = article.image_url ?
                    `<img src="${escapeHtml(article.image_url)}" alt="${escapeHtml(article.title || 'News image')}" class="news-list-image clickable-news-image" onclick="window.news?.openArticle('${escapeHtml(article.url || '')}')" onkeypress="if(event.key==='Enter') window.news?.openArticle('${escapeHtml(article.url || '')}')" tabindex="0" onerror="this.style.display='none'" loading="lazy" style="cursor: pointer;">` :
                    `<div class="news-list-image"></div>`;
                    
                const contentHtml = article.content ?
                    `<p class="news-list-summary">${escapeHtml(article.content.substring(0, 300))}...</p>` :
                    '';
                    
                const influence = article.article_metadata?.influence_tagging || {};
                const tags = Array.isArray(influence.tags) ? influence.tags : [];
                const category = escapeHtml(influence.category || '');
                const regions = Array.isArray(influence.regions) ? influence.regions : [];
                const tagsHtml = tags.slice(0, 3).map(tag => escapeHtml(tag)).join(', ');
                const regionsText = regions.map(region => escapeHtml(region)).join(', ');
                const categoryText = category || '';
                const influenceHtml = isMobile() ? this.renderInfluenceTagging(article, true) : 
                        `<div class="news-meta-primary">
                            ${categoryText ? `<span class="meta-separator">•</span><span class="meta-category">${categoryText}</span>` : ''}
                            ${regionsText ? `<span class="meta-separator">•</span><span class="meta-regions">${regionsText}</span>` : ''}
                            ${tagsHtml ? `<span class="meta-separator">•</span><span class="meta-tags">${tagsHtml}</span>` : ''}
                        </div>`; 

                return `
                    <div class="news-list-item">
                        ${imageHtml}
                        <div class="news-list-content">
                            <h6 class="news-list-title clickable-news-title" onclick="window.news?.openArticle('${escapeHtml(article.url || '')}')" onkeypress="if(event.key==='Enter') window.news?.openArticle('${escapeHtml(article.url || '')}')" tabindex="0" style="cursor: pointer;">${escapeHtml(article.title || 'Untitled')}</h6>
                            <div class="news-list-meta">
                                <div class="news-meta-primary">
                                    <span class="news-list-source">${escapeHtml(article.source)}</span>
                                    <span class="meta-separator">•</span>
                                    <span class="news-list-date">${formatDateWithTime(article.date || article.publish_time)}</span>
                                </div>
                                ${influenceHtml}
                            </div>
                            ${contentHtml}
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = articlesHtml;
        } catch (error) {
            console.error('Error rendering more articles:', error);
            container.innerHTML = '<div class="news-error">Error displaying articles</div>';
        }
    }

    setupScrollListener() {
        let ticking = false;
        let lastScrollTime = 0;
        const SCROLL_DEBOUNCE_DELAY = 250; // 250ms debounce delay
        
        const checkScroll = () => {
            const now = Date.now();
            
            // Debounce: only proceed if enough time has passed since last trigger
            if (now - lastScrollTime < SCROLL_DEBOUNCE_DELAY) {
                ticking = false;
                return;
            }
            
            const scrollHeight = document.documentElement.scrollHeight;
            const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
            const clientHeight = document.documentElement.clientHeight;
            
            // Trigger when user is 100px from bottom
            if (scrollHeight - scrollTop - clientHeight < 100) {
                if (!this.isLoading) {
                    lastScrollTime = now; // Update last trigger time
                    
                    if (this.isSearchActive && this.searchArticles.length > 0) {
                        this.loadSearchResults(false);
                    } else if (!this.isSearchActive && this.displayedMoreArticles.length > 0) {
                        this.loadMoreArticles();
                    }
                }
            }
            ticking = false;
        };
        
        const onScroll = () => {
            if (!ticking) {
                requestAnimationFrame(checkScroll);
                ticking = true;
            }
        };
        
        window.addEventListener('scroll', onScroll, { passive: true });
    }

    showPaginationLoading() {
        // Determine which container to use based on search state
        const containerId = this.isSearchActive ? 'search-results-list' : 'more-news-list';
        const container = document.getElementById(containerId);
        if (!container) return;
        
        const loadingText = this.isSearchActive ? 'Loading more search results...' : 'Loading more articles...';
        const loadingHtml = `
            <div class="pagination-loading">
                <div class="news-loading-spinner"></div>
                <p>${loadingText}</p>
            </div>
        `;
        
        const existingLoading = container.querySelector('.pagination-loading');
        if (!existingLoading) {
            container.insertAdjacentHTML('beforeend', loadingHtml);
        }
    }

    hidePaginationLoading() {
        // Check both regular news and search results containers
        const containers = [
            document.getElementById('more-news-list'),
            document.getElementById('search-results-list')
        ];
        
        containers.forEach(container => {
            if (!container) return;
            
            const loadingElement = container.querySelector('.pagination-loading');
            if (loadingElement) {
                loadingElement.remove();
            }
        });
    }

    showPaginationError() {
        // Determine which container to use based on search state
        const containerId = this.isSearchActive ? 'search-results-list' : 'more-news-list';
        const container = document.getElementById(containerId);
        if (!container) return;
        
        const errorText = this.isSearchActive ? 'Failed to load more search results. Please try scrolling again.' : 'Failed to load more articles. Please try scrolling again.';
        const errorHtml = `
            <div class="pagination-error" style="text-align: center; padding: 20px; color: #dc3545;">
                <i class="bi bi-exclamation-triangle"></i>
                ${errorText}
            </div>
        `;
        
        const existingError = container.querySelector('.pagination-error');
        if (!existingError) {
            container.insertAdjacentHTML('beforeend', errorHtml);
            // Auto-remove error after 3 seconds
            setTimeout(() => {
                const errorElement = container.querySelector('.pagination-error');
                if (errorElement) {
                    errorElement.remove();
                }
            }, 3000);
        }
    }

    performSearch() {
        const searchInput = document.getElementById('news-search-input');
        if (searchInput) {
            this.currentFilters.search = searchInput.value.trim();
            this.applyFilters();
        }
    }

    applyFilters() {
        // Get filter values
        this.currentFilters.dateFrom = document.getElementById('date-from')?.value || '';
        this.currentFilters.dateTo = document.getElementById('date-to')?.value || '';
        this.currentFilters.source = document.getElementById('news-source')?.value || '';
        this.currentFilters.sentiment = document.getElementById('sentiment-filter')?.value || '';

        // Check if any filters are active
        const hasActiveFilters = this.currentFilters.search || 
                                this.currentFilters.dateFrom || 
                                this.currentFilters.dateTo || 
                                this.currentFilters.source || 
                                this.currentFilters.sentiment;

        if (hasActiveFilters) {
            // Enter search mode
            this.isSearchActive = true;
            this.searchOffset = 0;
            this.searchArticles = [];
            this.loadSearchResults(true);
        } else {
            // Exit search mode and return to normal view
            this.exitSearchMode();
        }
    }

    async loadSearchResults(reset = false) {
        if (this.isLoading) return;
        
        try {
            this.isLoading = true;
            
            if (reset) {
                this.searchOffset = 0;
                this.searchArticles = [];
                this.hideSearchContainers();
                this.showSearchLoading();
            } else {
                this.showPaginationLoading();
            }

            // Build API parameters
            const params = new URLSearchParams();
            if (this.currentFilters.search) params.append('search', this.currentFilters.search);
            if (this.currentFilters.dateFrom) params.append('start_date', this.currentFilters.dateFrom);
            if (this.currentFilters.dateTo) params.append('end_date', this.currentFilters.dateTo);
            params.append('limit', '20');
            params.append('offset', this.searchOffset.toString());

            const response = await fetch(`/api/news?${params.toString()}`);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'Failed to load search results');
            }
            
            let articles = data.all || [];
            
            // Apply client-side filters
            articles = articles.filter(article => {
                // Source filter
                if (this.currentFilters.source && article.source.toLowerCase() !== this.currentFilters.source.toLowerCase()) {
                    return false;
                }
                
                // Sentiment filter
                if (this.currentFilters.sentiment) {
                    const sentiment = this.getSentimentFromArticle(article);
                    if (!sentiment || sentiment.toLowerCase() !== this.currentFilters.sentiment.toLowerCase()) {
                        return false;
                    }
                }
                
                return true;
            });

            // Add to search results
            this.searchArticles.push(...articles);
            this.searchOffset += articles.length;

            this.renderSearchResults();
            
        } catch (error) {
            console.error('Error loading search results:', error);
            this.showSearchError(error.message);
        } finally {
            this.isLoading = false;
        }
    }

    clearFilters() {
        try {
            // Clear all filter inputs safely
            const elements = [
                'news-search-input',
                'date-from', 
                'date-to',
                'news-source',
                'sentiment-filter'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.value = '';
                }
            });
            
            // Reset filters and reload
            this.currentFilters = {
                search: '',
                dateFrom: '',
                dateTo: '',
                source: '',
                sentiment: ''
            };
            
            // Exit search mode and show normal view
            this.exitSearchMode();
        } catch (error) {
            console.error('Error clearing filters:', error);
        }
    }

    exitSearchMode() {
        this.isSearchActive = false;
        this.searchOffset = 0;
        this.searchArticles = [];
        this.showNormalContainers();
        // Restore the more news content
        this.restoreMoreNewsContent();
    }

    hideSearchContainers() {
        // Hide the entire featured news section
        const featuredSection = document.querySelector('.featured-news-section');
        if (featuredSection) featuredSection.style.display = 'none';
        
        // Hide the more news section initially (but preserve content)
        const moreNewsSection = document.querySelector('.more-news-section');
        if (moreNewsSection && !moreNewsSection.classList.contains('search-results-section')) {
            moreNewsSection.style.display = 'none';
        }
    }

    showNormalContainers() {
        // Show the featured news section
        const featuredSection = document.querySelector('.featured-news-section');
        if (featuredSection) featuredSection.style.display = '';
        
        // Show the original more news section (not search results)
        const moreNewsSection = document.querySelector('.more-news-section:not(.search-results-section)');
        if (moreNewsSection) moreNewsSection.style.display = '';
        
        // Remove search results section if it exists
        const searchSection = document.querySelector('.search-results-section');
        if (searchSection) searchSection.remove();
    }

    restoreMoreNewsContent() {
        // Check if the more-news-list container exists and has content
        const container = document.getElementById('more-news-list');
        if (!container) return;
        
        // If container is empty but we have displayed articles, re-render them
        if (container.innerHTML.trim() === '' && this.displayedMoreArticles.length > 0) {
            this.renderMoreArticlesList();
        } else if (container.innerHTML.trim() === '' && this.moreArticles.length > 0) {
            // If we have articles but nothing displayed, render initial batch
            this.renderMoreNews();
        }
        
        // Debug log
        console.log('Restoring more news content:', {
            containerEmpty: container.innerHTML.trim() === '',
            displayedCount: this.displayedMoreArticles.length,
            totalCount: this.moreArticles.length
        });
    }

    showSearchLoading() {
        this.createSearchResultsSection();
        const container = document.getElementById('search-results-list');
        if (!container) return;
        
        const loadingHtml = `
            <div class="news-loading">
                <div class="news-loading-spinner"></div>
                <p>Searching articles...</p>
            </div>
        `;
        
        container.innerHTML = loadingHtml;
    }

    createSearchResultsSection() {
        // Remove existing search section if it exists
        const existingSection = document.querySelector('.search-results-section');
        if (existingSection) existingSection.remove();
        
        // Get search term for display
        let searchTerm = '';
        if (this.currentFilters.search) {
            searchTerm = this.currentFilters.search;
        } else {
            const filters = [];
            if (this.currentFilters.dateFrom || this.currentFilters.dateTo) {
                filters.push('Date Range');
            }
            if (this.currentFilters.source) {
                filters.push(`Source: ${this.currentFilters.source}`);
            }
            if (this.currentFilters.sentiment) {
                filters.push(`Sentiment: ${this.currentFilters.sentiment}`);
            }
            searchTerm = filters.length > 0 ? filters.join(', ') : 'Filtered Content';
        }
        
        // Create search results section HTML
        const searchSectionHtml = `
            <div class="search-results-section more-news-section">
                <h5 style="color: #1f2937; font-weight: 700; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                    <i class="bi bi-search" style="color: #3b82f6;"></i>
                    Search Results for "${escapeHtml(searchTerm)}"
                </h5>
                <div class="news-list" id="search-results-list">
                </div>
            </div>
        `;
        
        // Insert after the search section
        const searchSection = document.querySelector('.search-section');
        if (searchSection) {
            searchSection.insertAdjacentHTML('afterend', searchSectionHtml);
        }
    }

    showSearchError(message) {
        this.createSearchResultsSection();
        const container = document.getElementById('search-results-list');
        if (!container) return;
        
        const errorHtml = `
            <div class="news-error">
                <i class="bi bi-exclamation-triangle"></i>
                Error searching articles: ${escapeHtml(message)}
            </div>
        `;
        
        container.innerHTML = errorHtml;
    }

    renderSearchResults() {
        this.createSearchResultsSection();
        const container = document.getElementById('search-results-list');
        if (!container) return;

        this.hidePaginationLoading();

        if (this.searchArticles.length === 0) {
            container.innerHTML = '<div class="news-error">No articles found matching your search criteria</div>';
            return;
        }

        try {
            // Render articles using the same format as more news
            const articlesHtml = this.searchArticles.map(article => {
                const imageHtml = article.image_url ?
                    `<img src="${escapeHtml(article.image_url)}" alt="${escapeHtml(article.title || 'News image')}" class="news-list-image clickable-news-image" onclick="window.news?.openArticle('${escapeHtml(article.url || '')}')" onkeypress="if(event.key==='Enter') window.news?.openArticle('${escapeHtml(article.url || '')}')" tabindex="0" onerror="this.style.display='none'" loading="lazy" style="cursor: pointer;">` :
                    `<div class="news-list-image"></div>`;
                    
                const contentHtml = article.content ?
                    `<p class="news-list-summary">${escapeHtml(article.content.substring(0, 300))}...</p>` :
                    '';
                    
                const influence = article.article_metadata?.influence_tagging || {};
                const tags = Array.isArray(influence.tags) ? influence.tags : [];
                const category = escapeHtml(influence.category || '');
                const regions = Array.isArray(influence.regions) ? influence.regions : [];
                const tagsHtml = tags.slice(0, 3).map(tag => escapeHtml(tag)).join(', ');
                const regionsText = regions.map(region => escapeHtml(region)).join(', ');
                const categoryText = category || '';
                const influenceHtml = isMobile() ? this.renderInfluenceTagging(article, true) : 
                        `<div class="news-meta-primary">
                            ${categoryText ? `<span class="meta-separator">•</span><span class="meta-category">${categoryText}</span>` : ''}
                            ${regionsText ? `<span class="meta-separator">•</span><span class="meta-regions">${regionsText}</span>` : ''}
                            ${tagsHtml ? `<span class="meta-separator">•</span><span class="meta-tags">${tagsHtml}</span>` : ''}
                        </div>`; 

                return `
                    <div class="news-list-item">
                        ${imageHtml}
                        <div class="news-list-content">
                            <h6 class="news-list-title clickable-news-title" onclick="window.news?.openArticle('${escapeHtml(article.url || '')}')" onkeypress="if(event.key==='Enter') window.news?.openArticle('${escapeHtml(article.url || '')}')" tabindex="0" style="cursor: pointer;">${escapeHtml(article.title || 'Untitled')}</h6>
                            <div class="news-list-meta">
                                <div class="news-meta-primary">
                                    <span class="news-list-source">${escapeHtml(article.source)}</span>
                                    <span class="meta-separator">•</span>
                                    <span class="news-list-date">${formatDateWithTime(article.date || article.publish_time)}</span>
                                </div>
                                ${influenceHtml}
                            </div>
                            ${contentHtml}
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = articlesHtml;
        } catch (error) {
            console.error('Error rendering search results:', error);
            container.innerHTML = '<div class="news-error">Error displaying search results</div>';
        }
    }

    getSentimentFromArticle(article) {
        if (article.article_metadata && article.article_metadata.sentiment_analysis) {
            return article.article_metadata.sentiment_analysis.label;
        }
        return null;
    }

    formatDate(dateString) {
        if (!dateString) return 'Recent';

        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) return '1 day ago';
        if (diffDays < 7) return `${diffDays} days ago`;
        if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;

        return date.toLocaleDateString();
    }

    formatDateWithTime(dateString) {
        if (!dateString) return 'Recent';

        const date = new Date(dateString);

        // Convert to EST timezone
        const estDate = new Date(date.toLocaleString("en-US", {timeZone: "America/New_York"}));

        const options = {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
            timeZone: 'America/New_York'
        };

        return estDate.toLocaleDateString('en-US', options) + ' EST';
    }

    renderInfluenceTagging(article, compact = false) {
        try {
            if (!article?.article_metadata?.influence_tagging) {
                return '';
            }
            // Full version for main article
            const score = article.article_metadata.influence_tagging.influence;
            const category = escapeHtml(article.article_metadata.influence_tagging.category || 'N/A');
            const regions = Array.isArray(article.article_metadata.influence_tagging.regions) ? article.article_metadata.influence_tagging.regions : [];
            const tags = Array.isArray(article.article_metadata.influence_tagging.tags) ? article.article_metadata.influence_tagging.tags : [];
            const reason = escapeHtml(article.article_metadata.influence_tagging.reason || '');
            const scoreClass = score > 0 ? 'positive' : score < 0 ? 'negative' : 'neutral';
            const regionsText = regions.map(region => escapeHtml(region)).join(', ');
            const tooltipAttr = reason ? `data-tooltip="${reason}"` : '';
            const tooltipClass = reason ? 'has-tooltip' : '';
            const tagsHtml = tags.slice(0, 5).map(tag => `<span class="influence-tag">${escapeHtml(tag)}</span>`).join('');

            const influenceScoreHtml = window.isAdmin ? `<span class="influence-score ${scoreClass} ${tooltipClass}" ${tooltipAttr}>${score > 0 ? '+' : ''}${score}</span>` : '';
            
            return compact? `
                <div class="influence-tags">
                        <div class="influence-tags-list">
                            ${tagsHtml}
                        </div>
                        <div class="influence-meta">
                            <span> ${category} | ${regionsText}</span>
                        </div>
                    </div>` :
                `<div class="influence-tags">
                        <div class="influence-tags-list">
                            ${tagsHtml}
                        </div>
                        <div class="influence-meta">
                            <span> ${category} | ${regionsText}</span>
                            ${influenceScoreHtml}
                        </div>
                    </div>
                `;
        } catch (error) {
            console.error('Error rendering influence tagging:', error);
            return '';
        }
    }

    showLoading() {
        const featuredContainer = document.getElementById('featured-news-grid');
        const moreContainer = document.getElementById('more-news-list');
        
        const loadingHtml = `
            <div class="news-loading">
                <div class="news-loading-spinner"></div>
                <p>Loading news...</p>
            </div>
        `;
        
        if (featuredContainer) featuredContainer.innerHTML = loadingHtml;
        if (moreContainer) moreContainer.innerHTML = loadingHtml;
    }

    showError(message) {
        this.showFeaturedError(message);
        this.showMoreNewsError(message);
    }

    showFeaturedError(message) {
        const container = document.getElementById('featured-news-grid');

        const errorHtml = `
            <div class="news-error">
                <i class="bi bi-exclamation-triangle"></i>
                Error loading featured news: ${escapeHtml(message)}
            </div>
        `;

        if (container) container.innerHTML = errorHtml;
    }

    showMoreNewsError(message) {
        const moreContainer = document.getElementById('more-news-list');

        const errorHtml = `
            <div class="news-error">
                <i class="bi bi-exclamation-triangle"></i>
                Error loading more news: ${escapeHtml(message)}
            </div>
        `;

        if (moreContainer) moreContainer.innerHTML = errorHtml;
    }
    
    
    createArticleHtml({ article, className, sentimentBadge, contentLength, showInfluence }) {
        if (!article) return '';
        
        const title = escapeHtml(article.title || 'Untitled');
        const source = escapeHtml(article.source || 'Unknown Source');
        const url = escapeHtml(article.url || '');
        const imageUrl = escapeHtml(article.image_url || '');
        
        const imageHtml = imageUrl ?
            `<img src="${imageUrl}" alt="${title}" class="featured-article-image clickable-news-image" onclick="window.news?.openArticle('${url}')" onkeypress="if(event.key==='Enter') window.news?.openArticle('${url}')" tabindex="0" onerror="this.style.display='none'" loading="lazy" style="cursor: pointer;">` :
            `<div class="featured-article-image"></div>`;
            
        const contentHtml = article.content ?
            `<p class="featured-article-summary">${escapeHtml(article.content.substring(0, contentLength))}...</p>` :
            '';
            
        const influenceHtml = showInfluence ? this.renderInfluenceTagging(article) : '';
        
        return `
            <div class="${className}">
                ${imageHtml}
                <div class="featured-article-content">
                    <h6 class="featured-article-title clickable-news-title" onclick="window.news?.openArticle('${url}')" onkeypress="if(event.key==='Enter') window.news?.openArticle('${url}')" tabindex="0" style="cursor: pointer;">${title}</h6>
                    <div class="featured-article-meta">
                        <span class="featured-article-source">${source}</span>
                        <span class="news-list-date">${formatDateWithTime(article.date || article.publish_time)}</span>
                        ${sentimentBadge}
                    </div>
                    ${contentHtml}
                    ${influenceHtml}
                </div>
            </div>
        `;
    }
    
    openArticle(url) {
        if (url && typeof url === 'string') {
            try {
                // Validate URL before opening
                const urlObj = new URL(url);
                if (urlObj.protocol === 'http:' || urlObj.protocol === 'https:') {
                    window.open(url, '_blank', 'noopener,noreferrer');
                }
            } catch (error) {
                console.error('Invalid URL:', error);
            }
        }
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    try {
        window.news = new News();
    } catch (error) {
        console.error('Failed to initialize News:', error);
    }
});

// Handle page unload to cleanup
window.addEventListener('beforeunload', function() {
    if (window.news?.abortController) {
        window.news.abortController.abort();
    }
});
