/**
 * Price Graph JavaScript Module
 * Handles interactive price charts with multiple time ranges and real-time updates
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

// Configuration Constants for Price Graph
const PRICE_GRAPH_CONFIG = {
    CHART_COLORS: {
        PRIMARY: '#007bff',
        VOLUME: 'rgba(255,165,0,0.6)',
        UP: '#10b981',
        DOWN: '#ef4444',
        NEUTRAL: '#6b7280',
        SUCCESS: '#10b981',
        WARNING: '#f59e0b',
        BLUE: '#3b82f6',
        LIVE_DOT: '#ff4444',
        LIVE_PULSE: 'rgba(255, 68, 68, 0.3)'
    },

    TIME_RANGES: {
        '1D': { label: '1 Day', days: 1, interval: '1m' },
        '1W': { label: '1 Week', days: 7, interval: '15m' },
        '1M': { label: '1 Month', days: 30, interval: '1h' },
        '3M': { label: '3 Months', days: 90, interval: '1d' },
        '6M': { label: '6 Months', days: 180, interval: '1d' },
        '1Y': { label: '1 Year', days: 365, interval: '1d' },
        'YTD': { label: 'YTD', days: null, interval: '1d' }
    },

    CHART_TYPES: {
        LINE: 'line',
        CANDLESTICK: 'candlestick'
    },

    DOM_IDS: {
        PRICE_CHART: 'price-chart',
        TIME_RANGE_CONTAINER: 'time-range-selector',
        CHART_CONTAINER: 'chart-container',
        CHART_TYPE_CONTAINER: 'chart-type-selector',
        LIVE_INDICATOR: 'live-indicator',
        TICKER_SELECTOR: 'ticker-selector'
    },

    ANIMATIONS: {
        LIVE_DOT_DURATION: 1000,
        PULSE_DURATION: 2000,
        BLINK_DURATION: 800
    }
};

import { isMobile } from './utils.js';

/**
 * Price Graph API Service Class
 */
class PriceGraphAPI {
    async fetchChartData(ticker, timeRange) {
        const range = PRICE_GRAPH_CONFIG.TIME_RANGES[timeRange];
        let startDate, endDate, interval;
        
        endDate = new Date();
        
        if (timeRange === 'YTD') {
            startDate = new Date(endDate.getFullYear(), 0, 1); // January 1st of current year
        } else if (timeRange === '1D') {
            // For 1-day view, get intraday data
            startDate = new Date(endDate);
            return this.fetchIntradayData(ticker, startDate, endDate);
        } else {
            startDate = new Date(endDate);
            startDate.setDate(startDate.getDate() - range.days);
        }

        interval = range.interval;
        const params = new URLSearchParams({
            ticker: ticker,
            start_date: startDate.toISOString().split('T')[0],
            end_date: endDate.toISOString().split('T')[0],
            interval: interval
        });
        
        const url = `/api/stock-data?${params.toString()}`;        
        const response = await fetch(url);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || `Failed to load ${ticker} chart data (HTTP ${response.status})`);
        }
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        return data;
    }

    async fetchIntradayData(ticker, startDate, endDate) {
        // For intraday data, we'll use a different endpoint or modify the existing one
        const params = new URLSearchParams({
            ticker: ticker,
            period: '1d',
            interval: '1m'
        });        
        const url = `/api/stock-data?${params.toString()}`;        
        try {
            const response = await fetch(url);
            const data = await response.json();
            
            if (!response.ok) {
                // Fallback to daily data if intraday endpoint doesn't exist
                console.warn('Intraday endpoint not available, using daily data');
                return null;
            }
            
            return data;
        } catch (error) {
            console.warn('Intraday data fetch failed, using daily fallback:', error);
            return null;
        }
    }
}

/**
 * Price Graph Main Class
 */
class PriceGraph {
    constructor(ticker = 'SPY') {
        this.currentTicker = ticker;
        this.currentTimeRange = '1Y'; // Default to 1 year
        this.currentChartType = PRICE_GRAPH_CONFIG.CHART_TYPES.LINE; // Default to line chart
        this.chartData = null;
        this.isLoading = false;
        this.updateInterval = null;
        this.marketStatusInterval = null;
        this.abortController = null;
        this.isMarketOpen = false;
        this.lastUpdateTime = null;
        this.animationFrameId = null;
        this.liveDotVisible = false;
        this.isMobile = isMobile();
        
        // Initialize API service
        this.apiService = new PriceGraphAPI();
        
        this.init();
    }

    init() {
        // Ensure DOM is ready before initializing
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeComponents());
        } else {
            this.initializeComponents();
        }
    }
    
    initializeComponents() {
        this.createTickerSelector();
        this.createTimeRangeSelector();
        this.createChartTypeSelector();
        this.createLiveIndicator();
        this.setupEventListeners();
        this.loadChartData();
        this.setupRealTimeUpdates();
    }

    createTickerSelector() {
        const container = document.getElementById(PRICE_GRAPH_CONFIG.DOM_IDS.TICKER_SELECTOR);
        if (!container) {
            console.warn('Ticker selector container not found');
            return;
        }
        
        const selectorHtml = `
            <select class="ticker-select form-select form-select-sm" id="ticker-select">
                <option value="SPY" selected>SPY - SPDR S&P 500 ETF</option>
                <option value="VOO">VOO - Vanguard S&P 500 ETF</option>
                <option value="QQQ">QQQ - Invesco QQQ Trust</option>
                <option value="IWM">IWM - iShares Russell 2000 ETF</option>
                <option value="DIA">DIA - SPDR Dow Jones Industrial Average ETF</option>
            </select>
        `;
        container.innerHTML = selectorHtml;
    }

    createTimeRangeSelector() {
        const container = document.getElementById(PRICE_GRAPH_CONFIG.DOM_IDS.TIME_RANGE_CONTAINER);
        if (!container) {
            console.warn('Time range selector container not found');
            return;
        }

        if (this.isMobile) {
            // Mobile: Use dropdown
            const selectorHtml = `
                <select class="time-range-select form-select form-select-sm" id="time-range-select">
                    ${Object.entries(PRICE_GRAPH_CONFIG.TIME_RANGES).map(([key, range]) => `
                        <option value="${key}" ${key === this.currentTimeRange ? 'selected' : ''}>
                            ${range.label}
                        </option>
                    `).join('')}
                </select>
            `;
            container.innerHTML = selectorHtml;
        } else {
            // Desktop: Use buttons
            const selectorHtml = `
                <div class="time-range-buttons">
                    ${Object.entries(PRICE_GRAPH_CONFIG.TIME_RANGES).map(([key, range]) => `
                        <button 
                            class="time-range-btn ${key === this.currentTimeRange ? 'active' : ''}" 
                            data-range="${key}"
                            type="button"
                        >
                            ${range.label}
                        </button>
                    `).join('')}
                </div>
            `;
            container.innerHTML = selectorHtml;
        }
    }

    createChartTypeSelector() {
        const container = document.getElementById(PRICE_GRAPH_CONFIG.DOM_IDS.CHART_TYPE_CONTAINER);
        if (!container) {
            console.warn('Chart type selector container not found');
            return;
        }

        if (this.isMobile) {
            // Mobile: Use dropdown
            const selectorHtml = `
                <select class="chart-type-select form-select form-select-sm" id="chart-type-select">
                    <option value="${PRICE_GRAPH_CONFIG.CHART_TYPES.LINE}" ${this.currentChartType === PRICE_GRAPH_CONFIG.CHART_TYPES.LINE ? 'selected' : ''}>
                        Line Chart
                    </option>
                    <option value="${PRICE_GRAPH_CONFIG.CHART_TYPES.CANDLESTICK}" ${this.currentChartType === PRICE_GRAPH_CONFIG.CHART_TYPES.CANDLESTICK ? 'selected' : ''}>
                        Candlestick
                    </option>
                </select>
            `;
            container.innerHTML = selectorHtml;
        } else {
            // Desktop: Use buttons
            const selectorHtml = `
                <div class="chart-type-buttons">
                    <button 
                        class="chart-type-btn ${this.currentChartType === PRICE_GRAPH_CONFIG.CHART_TYPES.LINE ? 'active' : ''}" 
                        data-type="${PRICE_GRAPH_CONFIG.CHART_TYPES.LINE}"
                        type="button"
                        title="Line Chart"
                    >
                        <i class="bi bi-graph-up"></i>
                        Line
                    </button>
                    <button 
                        class="chart-type-btn ${this.currentChartType === PRICE_GRAPH_CONFIG.CHART_TYPES.CANDLESTICK ? 'active' : ''}" 
                        data-type="${PRICE_GRAPH_CONFIG.CHART_TYPES.CANDLESTICK}"
                        type="button"
                        title="Candlestick Chart"
                    >
                        <i class="bi bi-bar-chart"></i>
                        Candlestick
                    </button>
                </div>
            `;
            container.innerHTML = selectorHtml;
        }
    }

    createLiveIndicator() {
        const container = document.getElementById(PRICE_GRAPH_CONFIG.DOM_IDS.LIVE_INDICATOR);
        if (!container) return;

        if (this.isMarketOpen) {
            const indicatorHtml = `
                <div class="live-indicator active">
                    <div class="live-dot"></div>
                    <span class="live-text">LIVE</span>
                    <div class="live-pulse"></div>
                </div>
            `;
            container.innerHTML = indicatorHtml;
        } else {
            const indicatorHtml = `
                <div class="live-indicator closed">
                    <span class="live-text">CLOSED</span>
                </div>
            `;
            container.innerHTML = indicatorHtml;
        }
    }

    setupEventListeners() {
        // Time range selector
        const container = document.getElementById(PRICE_GRAPH_CONFIG.DOM_IDS.TIME_RANGE_CONTAINER);
        if (container) {
            if (this.isMobile) {
                // Mobile: dropdown event listener
                const select = container.querySelector('.time-range-select');
                if (select) {
                    select.addEventListener('change', (e) => {
                        const newRange = e.target.value;
                        if (newRange !== this.currentTimeRange) {
                            this.setTimeRange(newRange);
                        }
                    });
                }
            } else {
                // Desktop: button event listener
                container.addEventListener('click', (e) => {
                    if (e.target.classList.contains('time-range-btn')) {
                        const newRange = e.target.dataset.range;
                        if (newRange !== this.currentTimeRange) {
                            this.setTimeRange(newRange);
                        }
                    }
                });
            }
        }

        // Chart type selector
        const chartTypeContainer = document.getElementById(PRICE_GRAPH_CONFIG.DOM_IDS.CHART_TYPE_CONTAINER);
        if (chartTypeContainer) {
            if (this.isMobile) {
                // Mobile: dropdown event listener
                const select = chartTypeContainer.querySelector('.chart-type-select');
                if (select) {
                    select.addEventListener('change', (e) => {
                        const newType = e.target.value;
                        if (newType !== this.currentChartType) {
                            this.setChartType(newType);
                        }
                    });
                }
            } else {
                // Desktop: button event listener
                chartTypeContainer.addEventListener('click', (e) => {
                    if (e.target.classList.contains('chart-type-btn') || e.target.closest('.chart-type-btn')) {
                        const btn = e.target.classList.contains('chart-type-btn') ? e.target : e.target.closest('.chart-type-btn');
                        const newType = btn.dataset.type;
                        if (newType !== this.currentChartType) {
                            this.setChartType(newType);
                        }
                    }
                });
            }
        }

        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopAutoUpdate();
                this.stopLiveAnimations();
            } else {
                this.setupRealTimeUpdates();
            }
        });

        // Handle window resize to update mobile detection
        let resizeTimeout;
        let isUpdating = false; // Prevent recursive calls
        
        window.addEventListener('resize', () => {
            if (isUpdating) return; // Prevent recursion
            
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                console.log('Resize triggered, current mobile state:', this.isMobile);
                const wasMobile = this.isMobile;
                this.isMobile = isMobile(); // Make sure this calls the function
                console.log('Mobile state changed from', wasMobile, 'to', this.isMobile);

                if (wasMobile !== this.isMobile && !isUpdating) {
                    console.log('Updating UI for mobile change...');
                    isUpdating = true;
                    try {
                        this.createTimeRangeSelector();
                        this.createChartTypeSelector();
                        this.setupEventListeners();
                    } finally {
                        isUpdating = false;
                    }
                }
            }, 250); // Debounce for 250ms
        });    
    }

    setTimeRange(range) {
        if (!PRICE_GRAPH_CONFIG.TIME_RANGES[range]) {
            console.error('Invalid time range:', range);
            return;
        }

        this.currentTimeRange = range;
        this.updateActiveButton();
        this.loadChartData();
        this.setupRealTimeUpdates(); // Reset auto-update for new range
    }

    setChartType(type) {
        if (!Object.values(PRICE_GRAPH_CONFIG.CHART_TYPES).includes(type)) {
            console.error('Invalid chart type:', type);
            return;
        }

        this.currentChartType = type;
        this.updateActiveChartTypeButton();
        
        // Re-render chart with new type
        if (this.chartData) {
            this.renderChart(this.chartData);
        }
    }

    updateActiveButton() {
        const buttons = document.querySelectorAll('.time-range-btn');
        buttons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.range === this.currentTimeRange);
        });
    }

    updateActiveChartTypeButton() {
        const buttons = document.querySelectorAll('.chart-type-btn');
        buttons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.type === this.currentChartType);
        });
    }

    async loadChartData() {
        if (this.isLoading) {
            console.log('Chart already loading, skipping...');
            return;
        }
        
        this.isLoading = true;
        
        try {
            this.showChartLoading();

            // Cancel any ongoing request
            if (this.abortController) {
                this.abortController.abort();
            }
            this.abortController = new AbortController();
            const data = await this.apiService.fetchChartData(this.currentTicker, this.currentTimeRange);
            this.chartData = data;
            await this.renderChart(data);

        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('Error loading chart data:', error);
                this.showChartError(error.message);
            }
        } finally {
            this.isLoading = false;
            this.abortController = null;
        }
    }

    async loadRealtimePrice() {
        try {
            if (this.abortController) {
                this.abortController.abort();
            }
            this.abortController = new AbortController();
            const params = new URLSearchParams();
            params.append('ticker', this.currentTicker);
            const response = await fetch(`/api/latest-price?${params.toString()}`);
            if (!response.ok) {
                throw new Error(`Failed to load latest price (HTTP ${response.status})`);
            }
            const data = await response.json();
            if (data.error) {
                throw new Error(data.error);
            }
            console.log('Received latest price data:', data);

            // Update the chart data with the latest price
            const chartData = this.chartData;
            chartData.close.push(data.close);
            chartData.dates.push(data.dates);
            await this.renderChart(chartData);
        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('Real-time price request aborted');
                this.showChartError(error.message);
            }
        } finally {
            this.abortController = null;
        }
    }

    async setupRealTimeUpdates() {
        const updateMarketStatus = async () => {
            try {
                this.stopAutoUpdate(); // Clear any existing interval
                const response = await fetch('/api/is-market-open');
                const data = await response.json();
                if (data.error) {
                    console.error('Error checking market hours:', data.error);
                    return;
                }
                const wasOpen = this.isMarketOpen;
                this.isMarketOpen = data.isOpen;
                
                // Update live indicator if status changed
                if (wasOpen !== this.isMarketOpen) {
                    this.updateLiveIndicator();
                }
                
                if (this.isMarketOpen) {
                    console.log('Market is open - updating every 10 seconds');
                    this.startLiveAnimations();
                    // Update every 10 seconds during market hours
                    this.updateInterval = setInterval(() => {
                        this.loadRealtimePrice();
                    }, 10 * 1000);
                } else {
                    console.log('Market is closed - updating every 6 hours');
                    this.stopLiveAnimations();
                    // Update every 6 hours when market is closed
                    this.updateInterval = setInterval(() => {
                        this.loadRealtimePrice();
                    }, 6 * 3600 * 1000);
                }
            } catch (error) {
                console.error('Error checking market hours:', error);
            }
        };
        
        // Initial check
        updateMarketStatus();
        
        // Check market status every hour
        this.marketStatusInterval = setInterval(updateMarketStatus, 3600 * 1000);
    }

    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    showChartLoading() {
        const chartContainer = document.getElementById(PRICE_GRAPH_CONFIG.DOM_IDS.PRICE_CHART);
        if (chartContainer) {
            const loadingHtml = `
                <div class="loading-spinner" style="text-align: center; padding: 40px; min-height: 200px; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <div class="spinner-border text-primary" role="status" style="margin-bottom: 15px;">
                        <span class="visually-hidden">Loading chart...</span>
                    </div>
                    <p class="mt-2" style="color: #6c757d; font-size: 1rem;">Loading ${this.currentTicker} chart (${this.currentTimeRange})...</p>
                    <small style="color: #adb5bd; margin-top: 5px;">Please wait...</small>
                </div>
            `;
            chartContainer.innerHTML = loadingHtml;
        }
    }

    showChartError(message) {
        const chartContainer = document.getElementById(PRICE_GRAPH_CONFIG.DOM_IDS.PRICE_CHART);
        if (chartContainer) {
            const errorHtml = `
                <div class="error-message" style="text-align: center; padding: 40px; background: #fee2e2; border: 1px solid #fecaca; border-radius: 8px; color: #991b1b;">
                    <i class="bi bi-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px; display: block;"></i>
                    <strong>Error loading chart:</strong> ${message}
                </div>
            `;
            chartContainer.innerHTML = errorHtml;
        }
    }

    async renderChart(data) {
        const chartContainer = document.getElementById(PRICE_GRAPH_CONFIG.DOM_IDS.PRICE_CHART);
        if (!chartContainer) {
            this.showChartError('Chart container not found');
            return;
        }

        // Extract data from the API format
        const { dates, open, high, low, close, volume} = data;
        
        if (!dates || dates.length === 0) {
            console.error('Empty or invalid chart data arrays');
            this.showChartError(`No chart data available for ${this.currentTicker}`);
            return;
        }

        try {
            // Clear the loading spinner first
            chartContainer.innerHTML = '';
            
            const traces = this.createChartTraces(dates, open, high, low, close, volume);
            const layout = this.createChartLayout();
            const config = this.createChartConfig();

            await Plotly.newPlot(chartContainer, traces, layout, config);

            // Update the price summary if available
            this.updatePriceSummary(data);
            
            // Setup hover functionality
            this.setupChartHover(chartContainer, data);
            
            // Add live data point if market is open
            if (this.isMarketOpen) {
                this.addLiveDataPoint(chartContainer, close[close.length - 1]);
            }
            
            // Ensure live indicator is updated after chart renders
            this.updateLiveIndicator();
            
        } catch (error) {
            console.error('Error rendering chart:', error);
            this.showChartError(`Failed to render chart: ${error.message}`);
        }
    }

    createChartTraces(dates, open, high, low, close, volume) {
        // Calculate changes from first price for each point
        const firstPrice = close[0];
        const customData = close.map(price => {
            const change = price - firstPrice;
            const changePercent = firstPrice !== 0 ? (change / firstPrice) * 100 : 0;
            return {
                change: change,
                changePercent: changePercent,
                changeIcon: change >= 0 ? '▲' : '▼',
                changeSign: change >= 0 ? '+' : '',
                changeColor: change >= 0 ? '#28a745' : '#dc3545'
            };
        });
        const traces = [];

        // Main price trace - either line or candlestick
        if (this.currentChartType === PRICE_GRAPH_CONFIG.CHART_TYPES.CANDLESTICK) {
            // Candlestick chart
            traces.push({
                x: dates,
                open: open,
                high: high,
                low: low,
                close: close,
                type: 'candlestick',
                name: `${this.currentTicker} (${this.currentTimeRange})`,
                increasing: { line: { color: PRICE_GRAPH_CONFIG.CHART_COLORS.UP } },
                decreasing: { line: { color: PRICE_GRAPH_CONFIG.CHART_COLORS.DOWN } },
                yaxis: 'y1',
                // hovertemplate: 
                //     `<span style="color: #007bff;">📈 Open:</span> <b>$%{open:.2f}</b><br>` +
                //     `<span style="color: #28a745;">⬆ High:</span> <b>$%{high:.2f}</b><br>` +
                //     `<span style="color: #dc3545;">⬇ Low:</span> <b>$%{low:.2f}</b><br>` +
                //     `<span style="color: #6f42c1;">🔹 Close:</span> <b>$%{close:.2f}</b><br>` +
                //     `<extra></extra>`,
                showlegend: false
            });
        } else {
            // Line chart
            traces.push({
                x: dates,
                y: close,
                type: 'scatter',
                mode: 'lines',
                name: `${this.currentTicker} (${this.currentTimeRange})`,
                line: { 
                    color: PRICE_GRAPH_CONFIG.CHART_COLORS.PRIMARY, 
                    width: this.currentTimeRange === '1D' ? 2 : 3 
                },
                yaxis: 'y1',
                customdata: customData,
                // hovertemplate:
                //     `<b>%{x}</b><br>` + 
                //     `<span style="color: #007bff;">📈 Open:</span> <b>$%{open:.2f}</b><br>` +
                //     `<span style="color: #28a745;">⬆ High:</span> <b>$%{high:.2f}</b><br>` +
                //     `<span style="color: #007bff;">💰 Price:</span> <b>$%{y:.2f}</b><br>` +
                //     `<span style="color: %{customdata.changeColor}">📊 Change:</span> <b>%{customdata.changeSign}$%{customdata.change:.2f} (%{customdata.changeSign}%{customdata.changePercent:.2f}%)</b><br>` +
                //     `<extra></extra>`,
                showlegend: false
            });
        }

        // Volume trace
        traces.push({
            x: dates,
            y: volume,
            type: 'bar',
            name: 'Volume',
            marker: { color: PRICE_GRAPH_CONFIG.CHART_COLORS.VOLUME },
            yaxis: 'y2',
            hovertemplate: '<span style="color: #ff8c00;">📊 Volume:</span> <b>%{y:,}</b><br><extra></extra>',
            showlegend: false
        });

        return traces;
    }

    createChartLayout() {
        const range = PRICE_GRAPH_CONFIG.TIME_RANGES[this.currentTimeRange];
        
        // Mobile-specific layout adjustments
        const isMobile = this.isMobile;
        const chartHeight = isMobile ? 250 : 350;
        const margins = isMobile ? { t: 0, r: 40, b: 30, l: 45 } : { t: 10, r: 60, b: 40, l: 60 };
        
        return {
            xaxis: { 
                type: 'date', 
                showgrid: false,
                rangeslider: { visible: false },
                showspikes: !isMobile, // Disable spikes on mobile for better performance
                spikemode: 'across',
                spikesnap: 'cursor',
                spikecolor: 'rgba(0, 123, 255, 0.3)',
                spikethickness: 1,
                spikedash: 'dot',
                tickfont: { size: isMobile ? 10 : 12 }
            },
            yaxis: { 
                tickformat: '$.2f', 
                side: 'left', 
                showgrid: true, 
                gridcolor: '#f1f5f9',
                showspikes: !isMobile,
                spikemode: 'across',
                spikesnap: 'cursor',
                spikecolor: 'rgba(0, 123, 255, 0.3)',
                spikethickness: 1,
                spikedash: 'dot',
                tickfont: { size: isMobile ? 10 : 12 }
            },
            yaxis2: { 
                overlaying: 'y', 
                side: 'right', 
                showgrid: false,
                tickfont: { size: isMobile ? 9 : 11 }
            },
            hovermode: 'x unified',
            hoverdistance: isMobile ? 30 : 50,
            showlegend: false,
            margin: margins,
            plot_bgcolor: 'rgba(0,0,0,0)',
            paper_bgcolor: 'rgba(0,0,0,0)',
            height: chartHeight,
            hoverlabel: {
                bgcolor: 'rgba(240, 240, 240, 0.95)',
                bordercolor: 'rgba(200, 200, 200, 0.8)',
                font: { size: isMobile ? 11 : 13 }
            },
            dragmode: isMobile ? false : true
        };
    }

    createChartConfig() {
        const baseConfig = {
            responsive: true,
            displaylogo: false,
            modeBarButtonsToRemove: ['select2d', 'lasso2d'],
            // ... your other existing config options
        };

        // Add mobile-specific configurations
        if (this.isMobile) {
            return {
                ...baseConfig,
                scrollZoom: false,
                doubleClick: false,
                showTips: false,
                touchZoom: false,
                touchPan: false,
                displayModeBar: false, // Hide toolbar on mobile
                staticPlot: false // Keep hover interactions
            };
        }

        // Desktop configuration with zoom enabled
        return {
            ...baseConfig,
            scrollZoom: true,
            doubleClick: 'reset+autosize',
            displayModeBar: true
        };
    }
    
    updatePriceSummary(data) {
        // Update price summary information if the elements exist
        const { close: prices } = data;
        
        if (!prices || prices.length === 0) return;
        
        // Store prices for hover functionality
        this.pricesData = prices;
        
        const currentPrice = prices[prices.length - 1];
        const firstPrice = prices[0]; // Change from first price in range
        const change = currentPrice - firstPrice;
        const changePercent = firstPrice !== 0 ? (change / firstPrice) * 100 : 0;
        
        // Try to update chart header with current price info
        const chartHeaderElement = document.querySelector('[id="price-summary-container"]');
        if (chartHeaderElement) {
            const priceChangeClass = change >= 0 ? 'text-success' : 'text-danger';
            const priceChangeIcon = change >= 0 ? '▲' : '▼';
            
            chartHeaderElement.innerHTML = `
                <small class="${priceChangeClass}" id="price-summary">
                    $${currentPrice.toFixed(2)} ${priceChangeIcon} ${change >= 0 ? '+' : ''}${change.toFixed(2)} (${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%)
                </small>
            `;
        }
    }

    setupChartHover(chartContainer, data) {
        const { close: prices } = data;
        const firstPrice = prices[0];
        
        // Setup hover event listener
        chartContainer.on('plotly_hover', (eventData) => {
            if (eventData.points && eventData.points.length > 0) {
                const point = eventData.points[0];
                // Only handle hover on the price trace (trace 0)
                if (point.curveNumber === 0) {
                    const hoveredPrice = point.y || point.close;
                    const change = hoveredPrice - firstPrice;
                    const changePercent = firstPrice !== 0 ? (change / firstPrice) * 100 : 0;
                    
                    this.updatePriceSummaryOnHover(hoveredPrice, change, changePercent);
                }
            }
        });
        
        // Setup unhover event to restore original summary
        chartContainer.on('plotly_unhover', () => {
            this.updatePriceSummary(data);
        });
    }
    
    updatePriceSummaryOnHover(hoveredPrice, change, changePercent) {
        const priceSummaryElement = document.getElementById('price-summary');

        if (priceSummaryElement) {
            const priceChangeClass = change >= 0 ? 'text-success' : 'text-danger';
            const priceChangeIcon = change >= 0 ? '▲' : '▼';
            
            priceSummaryElement.className = priceChangeClass;
            priceSummaryElement.innerHTML = `
                $${hoveredPrice.toFixed(2)} ${priceChangeIcon} ${change >= 0 ? '+' : ''}${change.toFixed(2)} (${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%)
            `;
        }
    }

    // Public method to change ticker
    setTicker(ticker) {
        this.currentTicker = ticker;
        this.loadChartData();
    }

    // Public method to get current state
    getCurrentState() {
        return {
            ticker: this.currentTicker,
            timeRange: this.currentTimeRange,
            isLoading: this.isLoading
        };
    }

    startLiveAnimations() {
        if (!this.isMarketOpen) return;
        
        // Start live dot pulse animation
        this.animateLiveDot();
    }
    
    stopLiveAnimations() {
        // Stop animation frame
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
    }
    
    animateLiveDot() {
        if (!this.isMarketOpen) return;
        
        const liveDot = document.querySelector('.live-dot');
        if (liveDot) {
            this.liveDotVisible = !this.liveDotVisible;
            liveDot.style.opacity = this.liveDotVisible ? '1' : '0.2';
            liveDot.style.transition = 'opacity 0.3s ease-in-out';
        }
        
        // Also animate the live data point on the chart
        this.animateLiveDataPoint();
        
        // Continue animation with setInterval for consistent timing
        this.animationFrameId = setTimeout(() => {
            this.animateLiveDot();
        }, PRICE_GRAPH_CONFIG.ANIMATIONS.BLINK_DURATION);
    }
    
    animateLiveDataPoint() {
        if (!this.isMarketOpen) return;
        
        const chartContainer = document.getElementById(PRICE_GRAPH_CONFIG.DOM_IDS.PRICE_CHART);
        if (!chartContainer || !chartContainer.data) return;
        
        // Find the live price trace
        const liveTraceIndex = chartContainer.data.findIndex(trace => trace.name === 'Live Price');
        if (liveTraceIndex >= 0) {
            const opacity = this.liveDotVisible ? 1.0 : 0.2;
            Plotly.restyle(chartContainer, {
                'marker.opacity': [opacity]
            }, liveTraceIndex);
        }
    }
    
    updateLiveIndicator() {
        this.createLiveIndicator();
        
        if (this.isMarketOpen) {
            this.startLiveAnimations();
        } else {
            this.stopLiveAnimations();
        }
    }
    
    addLiveDataPoint(chartContainer, currentPrice) {
        if (!this.isMarketOpen || !chartContainer) return;
        
        const chartData = chartContainer.data;
        if (!chartData || chartData.length === 0) return;
        
        // Find the price trace (first trace that's not volume)
        const priceTrace = chartData.find(trace => trace.yaxis !== 'y2');
        if (!priceTrace) return;
        
        // Add live data point with animation
        const lastIndex = priceTrace.x.length - 1;
        const lastDate = priceTrace.x[lastIndex];
        
        // Create live point trace with blinking animation
        const opacity = this.liveDotVisible ? 1.0 : 0.2;
        const livePointTrace = {
            x: [lastDate],
            y: [currentPrice],
            type: 'scatter',
            mode: 'markers',
            marker: {
                color: PRICE_GRAPH_CONFIG.CHART_COLORS.LIVE_DOT,
                size: 8,
                symbol: 'circle',
                opacity: opacity,
                line: {
                    color: PRICE_GRAPH_CONFIG.CHART_COLORS.LIVE_PULSE,
                    width: 2
                }
            },
            name: 'Live Price',
            showlegend: false,
            hovertemplate: `Live: $${currentPrice.toFixed(2)}<extra></extra>`
        };
        
        // Add or update live point
        const existingLiveTrace = chartData.findIndex(trace => trace.name === 'Live Price');
        if (existingLiveTrace >= 0) {
            Plotly.restyle(chartContainer, {
                x: [[lastDate]],
                y: [[currentPrice]],
                'marker.opacity': [opacity]
            }, existingLiveTrace);
        } else {
            Plotly.addTraces(chartContainer, livePointTrace);
        }
    }

    // Cleanup method
    destroy() {
        this.stopAutoUpdate();
        this.stopLiveAnimations();
        if (this.abortController) {
            this.abortController.abort();
        }
        if (this.marketStatusInterval) {
            clearInterval(this.marketStatusInterval);
        }
    }
}

// Export for use in other modules
window.PriceGraph = PriceGraph;
