# NewsMonitor GCP Deployment Guide (Cost-Optimized)

This guide provides a cost-optimized deployment solution for the NewsMonitor financial news web crawler application on Google Cloud Platform, using Cloud Scheduler instead of Celery/Redis for background tasks.

## 💰 Cost Optimization Overview

**Monthly Savings: $42-82 (34-29% reduction)**
- ❌ No Redis/Memorystore: ~$25-30/month saved
- ❌ No Celery worker container: ~$15-45/month saved  
- ✅ Simplified VPC networking: ~$2-7/month saved
- ✅ Same functionality with HTTP endpoints

## Architecture Comparison

### Before: Traditional Celery Architecture
```
Web App + Celery Worker + Redis + Cloud SQL + Scheduler
Monthly Cost: $122-282
```

### After: Cloud Scheduler Architecture  
```
Web App + Cloud SQL + Scheduler (HTTP endpoints)
Monthly Cost: $80-200 (34-29% savings)
```

## Table of Contents

1. [Quick Start](#quick-start)
2. [Architecture Design](#architecture-design)
3. [Cost Analysis](#cost-analysis)
4. [Deployment Steps](#deployment-steps)
5. [Background Tasks Migration](#background-tasks-migration)
6. [Monitoring & Maintenance](#monitoring--maintenance)

## Quick Start

### Prerequisites
```bash
# Set up environment
export GOOGLE_CLOUD_PROJECT="your-project-id"
export GOOGLE_CLOUD_LOCATION="us-central1"

# Enable required APIs (no Redis needed)
gcloud services enable run.googleapis.com sql-component.googleapis.com \
  sqladmin.googleapis.com secretmanager.googleapis.com \
  cloudbuild.googleapis.com cloudscheduler.googleapis.com
```

### 5-Step Deployment
```bash
cd deployment/scripts

# 1. Create infrastructure (without Redis)
./setup-infrastructure-no-celery.sh

# 2. Configure secrets
./setup-secrets.sh

# 3. Build and deploy (without Celery)
./build-and-deploy-no-celery.sh

# 4. Set up scheduler jobs
./setup-scheduler-no-celery.sh

# 5. Test deployment
curl $WEB_URL/health
```

## Architecture Design

### Optimized GCP Services

```
┌─────────────────────────────────────────────────────────────────┐
│                        Google Cloud Platform                    │
├─────────────────────────────────────────────────────────────────┤
│  Cloud Load Balancer + SSL Certificate                         │
├─────────────────────────────────────────────────────────────────┤
│  Cloud Run Services                                            │
│  ├── Web Application (Flask + Scheduler Endpoints)             │
│  │   ├── Auto-scaling: 1-10 instances                         │
│  │   ├── CPU: 1 vCPU, Memory: 2GB                            │
│  │   └── Includes background task HTTP endpoints              │
│  └── News Crawler (Scrapy)                                     │
│      ├── Scheduled execution via Cloud Scheduler              │
│      └── CPU: 2 vCPU, Memory: 4GB                            │
├─────────────────────────────────────────────────────────────────┤
│  Cloud SQL (PostgreSQL)                                        │
│  ├── Instance: db-n1-standard-1                               │
│  └── Storage: 20GB SSD with auto-resize                       │
├─────────────────────────────────────────────────────────────────┤
│  Cloud Scheduler (Replaces Celery/Redis)                       │
│  ├── Daily email summaries                                     │
│  ├── Weekly email summaries                                    │
│  ├── Market alerts (periodic checks)                          │
│  ├── Database cleanup                                          │
│  ├── Crawler execution (every 3 hours)                        │
│  └── Model retraining                                          │
├─────────────────────────────────────────────────────────────────┤
│  Secret Manager + Cloud Storage + Monitoring                   │
└─────────────────────────────────────────────────────────────────┘
```

### Removed Components (Cost Savings)
- ❌ **Cloud Memorystore (Redis)**: $25-30/month
- ❌ **Celery Worker Container**: $15-45/month
- ❌ **Complex VPC networking**: $2-7/month

## Cost Analysis

### Monthly Cost Breakdown

| Service | Cost | Notes |
|---------|------|-------|
| **Cloud Run (Web)** | $20-100 | Includes scheduler endpoints |
| **Cloud Run (Crawler)** | $5-15 | On-demand execution |
| **Cloud SQL** | $50-70 | PostgreSQL database |
| **Cloud Storage** | $1-5 | Logs and artifacts |
| **Cloud Scheduler** | $1-2 | All background tasks |
| **VPC Connector** | $3-8 | Smaller footprint |
| **Total** | **$80-200** | **34-29% savings** |

### Comparison with Original Architecture

| Component | Original | Optimized | Savings |
|-----------|----------|-----------|---------|
| Total Monthly Cost | $122-282 | $80-200 | $42-82 |
| Infrastructure Complexity | High | Low | Simplified |
| Maintenance Overhead | High | Low | Reduced |

## Deployment Steps

### Step 1: Infrastructure Setup (No Redis)
```bash
./setup-infrastructure-no-celery.sh
```
Creates:
- VPC network (smaller footprint)
- Cloud SQL PostgreSQL
- Cloud Storage bucket
- App Engine (for Cloud Scheduler)
- **Skips**: Redis/Memorystore

### Step 2: Secrets Configuration
```bash
./setup-secrets.sh
```
Same as original, but no Redis connection strings needed.

### Step 3: Build and Deploy (No Celery)
```bash
./build-and-deploy-no-celery.sh
```
Builds:
- Base container image
- Web application (with scheduler endpoints)
- Crawler container
- **Skips**: Celery worker container

### Step 4: Cloud Scheduler Setup
```bash
./setup-scheduler-no-celery.sh
```
Creates HTTP-based scheduled jobs:
- Daily email summaries → `/api/scheduler/daily-summary`
- Weekly email summaries → `/api/scheduler/weekly-summary`
- Market alerts → `/api/scheduler/market-alerts`
- Database cleanup → `/api/scheduler/cleanup-database`
- Crawler execution → `/api/admin/run-crawler`

## Background Tasks Migration

### HTTP Endpoints Replace Celery Tasks

| Original Celery Task | New HTTP Endpoint | Schedule |
|---------------------|-------------------|----------|
| `send_daily_summaries_task` | `POST /api/scheduler/daily-summary` | Daily 10 AM ET |
| `send_weekly_summaries_task` | `POST /api/scheduler/weekly-summary` | Monday 9 AM ET |
| `send_market_alert_task` | `POST /api/scheduler/market-alerts` | Hourly (market hours) |
| `cleanup_old_email_logs_task` | `POST /api/scheduler/cleanup-email-logs` | Weekly Sunday 4 AM |
| `send_targeted_emails_task` | `POST /api/scheduler/targeted-emails` | On-demand |

### Authentication & Security
```python
# Scheduler endpoints require Cloud Scheduler headers
@require_scheduler_auth
def send_daily_summaries():
    # Checks for X-CloudScheduler headers
    # Or admin authentication
    # Or Google-Cloud-Scheduler User-Agent
```

### Testing Endpoints
```bash
# Test daily summary endpoint
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-CloudScheduler: true" \
  $WEB_URL/api/scheduler/daily-summary

# Test market alerts
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-CloudScheduler: true" \
  $WEB_URL/api/scheduler/market-alerts
```

## Code Changes Required

### 1. Add Scheduler Endpoints to Web App
```python
# In web/app.py
from deployment.code_changes.scheduler_endpoints import scheduler_bp
app.register_blueprint(scheduler_bp)
```

### 2. Update Configuration (Remove Celery)
```python
# In web/config.py
class Config:
    # Remove Celery configuration
    # CELERY_BROKER_URL = ...
    # CELERY_RESULT_BACKEND = ...
    
    # Add scheduler flag
    DISABLE_CELERY = os.getenv('DISABLE_CELERY', 'false').lower() == 'true'
```

### 3. Conditional Celery Initialization
```python
# In web/__init__.py
def create_app():
    # ... existing code ...
    
    # Only initialize Celery if not disabled
    if not app.config.get('DISABLE_CELERY', False):
        init_celery(app)
    
    # Register scheduler endpoints
    from deployment.code_changes.scheduler_endpoints import scheduler_bp
    app.register_blueprint(scheduler_bp)
```

## Monitoring & Maintenance

### Cloud Scheduler Monitoring
```bash
# View job status
gcloud scheduler jobs list --location=$REGION

# View job logs
gcloud logging read 'resource.type="cloud_scheduler_job"' --limit=20

# Run job manually
gcloud scheduler jobs run newsmonitor-daily-summary --location=$REGION
```

### Application Monitoring
```bash
# View web service logs
gcloud run services logs read newsmonitor-web --region=$REGION

# Monitor scheduler endpoint calls
gcloud logging read 'resource.type="cloud_run_revision" AND httpRequest.requestUrl:"/api/scheduler/"'
```

## Benefits Summary

### ✅ Cost Benefits
- **$42-82/month savings** (34-29% reduction)
- **$504-984/year savings**
- Simplified billing (fewer services)

### ✅ Operational Benefits
- **Simpler architecture** (fewer moving parts)
- **Better observability** (HTTP logs vs queue monitoring)
- **Easier debugging** (direct endpoint testing)
- **Built-in retry logic** (Cloud Scheduler handles retries)

### ✅ Reliability Benefits
- **No Redis dependency** (eliminates single point of failure)
- **Automatic scaling** (Cloud Scheduler is fully managed)
- **Better error handling** (HTTP status codes)

### ⚠️ Trade-offs
- **No real-time triggers** (scheduled vs event-driven)
- **HTTP timeout limits** (60-minute max for Cloud Run)
- **No task queuing** (immediate execution)

## Migration from Existing Celery Deployment

### Migration Steps
1. **Deploy new endpoints** alongside existing Celery tasks
2. **Test scheduler jobs** in parallel with Celery
3. **Switch traffic** to scheduler endpoints
4. **Remove Celery infrastructure** after validation
5. **Clean up** Redis and worker containers

### Rollback Plan
- Keep original deployment configuration
- Switch Cloud Scheduler jobs back to Celery endpoints
- Redeploy Redis and Celery worker if needed

## Conclusion

The Cloud Scheduler approach provides significant cost savings while maintaining all functionality. The simplified architecture reduces operational complexity and improves reliability. For most NewsMonitor deployments, this optimization is highly recommended.

**Next Steps:**
1. Review the [detailed cost analysis](COST_ANALYSIS.md)
2. Follow the [deployment steps](#deployment-steps)
3. Test the optimized architecture
4. Monitor cost savings in GCP billing
