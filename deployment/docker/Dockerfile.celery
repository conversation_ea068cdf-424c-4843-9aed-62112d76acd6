# Celery worker container
FROM newsmonitor-base:latest

# Set environment variables for Celery
ENV CELERY_BROKER_URL=redis://redis:6379/0 \
    CELERY_RESULT_BACKEND=redis://redis:6379/0 \
    CELERY_WORKER_CONCURRENCY=4 \
    CELERY_WORKER_PREFETCH_MULTIPLIER=1 \
    CELERY_TASK_SOFT_TIME_LIMIT=300 \
    CELERY_TASK_TIME_LIMIT=600

# Switch to app user
USER appuser

# Working directory
WORKDIR /app/web

# Health check for Celery worker
HEALTHCHECK --interval=60s --timeout=30s --start-period=30s --retries=3 \
    CMD celery -A celery_worker.celery inspect ping || exit 1

# Start Celery worker and beat scheduler
CMD ["celery", "-A", "celery_worker.celery", "worker", "--loglevel=info", "--beat"]
