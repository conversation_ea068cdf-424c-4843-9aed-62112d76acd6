# News crawler container
FROM newsmonitor-base:latest

# Set environment variables for crawler
ENV SCRAPY_SETTINGS_MODULE=news_crawler.settings \
    CRAWLER_LOG_LEVEL=INFO \
    CRAWLER_CONCURRENT_REQUESTS=16 \
    CRAWLER_DOWNLOAD_DELAY=1

# Install additional crawler-specific dependencies
RUN pip install scrapy-splash==0.8.0

# Copy crawler-specific configuration
COPY deployment/config/scrapy.cfg /app/news_crawler/

# Create crawler output directory
RUN mkdir -p /app/news_crawler/output && \
    chown -R appuser:appuser /app/news_crawler/output

# Switch to app user
USER appuser

# Working directory for crawler
WORKDIR /app/news_crawler

# Health check for crawler (check if process is running)
HEALTHCHECK --interval=60s --timeout=30s --start-period=10s --retries=3 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# Default command - run all spiders
CMD ["python", "run_spiders.py", "--log-level", "INFO"]
