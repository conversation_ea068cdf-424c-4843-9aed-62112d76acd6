# Web application container
FROM newsmonitor-base:latest

# Set environment variables for web app
ENV PORT=8080 \
    FLASK_APP=web.app \
    FLASK_ENV=production \
    GUNICORN_WORKERS=4 \
    GUNICORN_THREADS=2 \
    GUNICORN_TIMEOUT=120

# Install additional web-specific dependencies
RUN pip install gunicorn[gevent]==21.2.0

# Copy web-specific configuration
COPY deployment/config/gunicorn.conf.py /app/

# Expose port
EXPOSE 8080

# Health check endpoint
COPY deployment/scripts/health_check.py /app/

# Switch to app user
USER appuser

# Start command
CMD ["gunicorn", "--config", "gunicorn.conf.py", "web.app:app"]
