apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: newsmonitor-web
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Scaling configuration
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/cpu-throttling: "false"
        
        # VPC connector for private resources
        run.googleapis.com/vpc-access-connector: newsmonitor-connector
        run.googleapis.com/vpc-access-egress: private-ranges-only
        
        # Cloud SQL connection
        run.googleapis.com/cloudsql-instances: PROJECT_ID:REGION:newsmonitor-db
        
        # Startup probe configuration
        run.googleapis.com/startup-cpu-boost: "true"
    spec:
      containerConcurrency: 80
      timeoutSeconds: 300
      serviceAccountName: newsmonitor-web-sa
      containers:
      - name: newsmonitor-web
        image: gcr.io/PROJECT_ID/newsmonitor-web:latest
        ports:
        - name: http1
          containerPort: 8080
        env:
        - name: SERVICE_TYPE
          value: "web"
        - name: SERVICE_NAME
          value: "newsmonitor-web"
        - name: PORT
          value: "8080"
        - name: FLASK_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-url
              key: url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: flask-secrets
              key: secret-key
        - name: SECURITY_PASSWORD_SALT
          valueFrom:
            secretKeyRef:
              name: flask-secrets
              key: password-salt
        - name: CELERY_BROKER_URL
          value: "redis://REDIS_IP:6379/0"
        - name: CELERY_RESULT_BACKEND
          value: "redis://REDIS_IP:6379/0"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: openai-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: anthropic-key
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: gemini-key
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: GOOGLE_CLOUD_LOCATION
          value: "REGION"
        - name: SERVICE_ACCOUNT_CREDENTIALS
          value: "/app/credentials/service-account.json"
        - name: MAIL_SERVER
          value: "smtp.gmail.com"
        - name: MAIL_PORT
          value: "587"
        - name: MAIL_USERNAME
          valueFrom:
            secretKeyRef:
              name: email-config
              key: username
        - name: MAIL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: email-config
              key: password
        - name: MAIL_DEFAULT_SENDER
          valueFrom:
            secretKeyRef:
              name: email-config
              key: default-sender
        resources:
          limits:
            cpu: "1000m"
            memory: "2Gi"
          requests:
            cpu: "500m"
            memory: "1Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        volumeMounts:
        - name: service-account
          mountPath: /app/credentials
          readOnly: true
      volumes:
      - name: service-account
        secret:
          secretName: service-account-key
