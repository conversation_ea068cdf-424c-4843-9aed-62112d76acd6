# Base requirements for NewsMonitor deployment
# This file consolidates all requirements for containerization

# Core Python dependencies
python-dotenv>=1.0.0

# Web framework and server
Flask>=2.0.0
gunicorn[gevent]==21.2.0
yfinance>=0.2.0
pandas>=1.3.0
plotly>=5.0.0
python-dateutil>=2.8.0
requests>=2.25.0

# Authentication and Security
Flask-Login>=0.6.0
Flask-WTF>=1.1.0
WTForms>=3.0.0
bcrypt>=4.0.0
email-validator>=2.0.0

# Email functionality
Flask-Mail>=0.9.1

# Background tasks
Celery>=5.3.0
redis>=4.5.0

# Database
SQLAlchemy>=2.0.0
Flask-SQLAlchemy>=3.0.0
Flask-Migrate>=4.0.0
Flask-Cors>=6.0.0
psycopg2-binary>=2.9.0

# Google Cloud dependencies
google-cloud-secret-manager>=2.16.0
google-cloud-storage>=2.10.0
google-cloud-logging>=3.8.0
google-cloud-monitoring>=2.15.0

# Web crawling
Scrapy>=2.11.0
newspaper4k>=0.9.0
beautifulsoup4>=4.12.0
lxml>=4.9.0
scrapy-splash>=0.8.0

# ML and NLP
torch>=2.0.1
transformers>=4.30.2
datasets>=2.14.5
numpy>=1.24.3
scikit-learn>=1.0.0
matplotlib>=3.4.0
tqdm>=4.60.0
accelerate>=0.20.0
sentencepiece>=0.1.99
tensorboard>=2.10.0

# LLM APIs
openai>=1.0.0
anthropic>=0.25.0
google-genai>=0.3.0

# Utilities
python-dateutil>=2.8.0
pytz>=2023.3
exchange-calendars>=4.2.0
