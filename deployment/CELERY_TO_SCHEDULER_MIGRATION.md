# NewsMonitor: Celery to Cloud Scheduler Migration Guide

## Executive Summary

This migration replaces the Celery/Redis infrastructure with Cloud Scheduler HTTP endpoints, providing **$42-82/month savings (34-29% cost reduction)** while maintaining all background task functionality.

## Migration Overview

### What's Being Replaced
- ❌ **Cloud Memorystore (Redis)**: $25-30/month → $0
- ❌ **Celery Worker Container**: $15-45/month → $0  
- ❌ **Complex VPC networking**: $5-15/month → $3-8/month
- ✅ **Background tasks**: Celery tasks → HTTP endpoints

### What Stays the Same
- ✅ **All functionality preserved**: Email summaries, alerts, cleanup tasks
- ✅ **Same scheduling**: Daily/weekly/hourly execution maintained
- ✅ **Web application**: Core Flask app unchanged
- ✅ **Database**: PostgreSQL remains the same
- ✅ **Crawler**: Scrapy crawler unchanged

## Files Created for Migration

### 1. HTTP Endpoint Replacements
```
deployment/code-changes/scheduler_endpoints.py
```
- Converts all Celery tasks to HTTP endpoints
- Includes authentication for Cloud Scheduler
- Maintains same functionality and error handling

### 2. Updated Deployment Configuration
```
deployment/cloud-run/web-service-no-celery.yaml
deployment/docker/Dockerfile.web-no-celery
```
- Removes Redis/Celery dependencies
- Adds DISABLE_CELERY environment variable
- Simplified container configuration

### 3. Infrastructure Scripts (No Redis)
```
deployment/scripts/setup-infrastructure-no-celery.sh
deployment/scripts/build-and-deploy-no-celery.sh
deployment/scripts/setup-scheduler-no-celery.sh
```
- Skips Redis/Memorystore creation
- Removes Celery worker deployment
- Creates Cloud Scheduler jobs for HTTP endpoints

### 4. Cost Analysis Documentation
```
deployment/COST_ANALYSIS.md
deployment/README_OPTIMIZED.md
```
- Detailed cost comparison
- Migration benefits and trade-offs
- Updated deployment guide

## Task Mapping: Celery → Cloud Scheduler

| Celery Task | HTTP Endpoint | Cloud Scheduler Job | Schedule |
|-------------|---------------|-------------------|----------|
| `send_daily_summaries_task` | `POST /api/scheduler/daily-summary` | `newsmonitor-daily-summary` | Daily 10 AM ET |
| `send_weekly_summaries_task` | `POST /api/scheduler/weekly-summary` | `newsmonitor-weekly-summary` | Monday 9 AM ET |
| `send_market_alert_task` | `POST /api/scheduler/market-alerts` | `newsmonitor-market-alerts` | Hourly (market hours) |
| `cleanup_old_email_logs_task` | `POST /api/scheduler/cleanup-email-logs` | `newsmonitor-email-logs-cleanup` | Weekly Sunday 4 AM |
| `send_targeted_emails_task` | `POST /api/scheduler/targeted-emails` | On-demand (admin triggered) | Manual |

## Migration Steps

### Option A: New Deployment (Recommended)
```bash
# Use optimized deployment scripts
cd deployment/scripts
./setup-infrastructure-no-celery.sh
./setup-secrets.sh
./build-and-deploy-no-celery.sh
./setup-scheduler-no-celery.sh
```

### Option B: Migrate Existing Deployment
```bash
# 1. Add scheduler endpoints to existing web app
# 2. Deploy updated web service
./build-and-deploy-no-celery.sh

# 3. Create Cloud Scheduler jobs
./setup-scheduler-no-celery.sh

# 4. Test scheduler endpoints
curl -X POST -H "X-CloudScheduler: true" $WEB_URL/api/scheduler/daily-summary

# 5. Remove Redis and Celery infrastructure
gcloud redis instances delete newsmonitor-redis --region=$REGION
gcloud run services delete newsmonitor-celery --region=$REGION
```

## Code Changes Required

### 1. Add Scheduler Endpoints
```python
# In web/app.py, add:
from deployment.code_changes.scheduler_endpoints import scheduler_bp
app.register_blueprint(scheduler_bp)
```

### 2. Conditional Celery Initialization
```python
# In web/__init__.py, modify:
def create_app():
    # ... existing code ...
    
    # Only initialize Celery if not disabled
    if not app.config.get('DISABLE_CELERY', False):
        init_celery(app)
    
    # Register scheduler endpoints
    from deployment.code_changes.scheduler_endpoints import scheduler_bp
    app.register_blueprint(scheduler_bp)
    
    return app
```

### 3. Update Admin Endpoints
```python
# Replace Celery task calls with direct function calls
# From:
task = send_targeted_emails_task.delay(email_type, recipient_filter, user_ids)

# To:
from web.email_service.service import EmailService
email_service = EmailService()
result = email_service.send_targeted_emails(email_type, recipient_filter, user_ids)
```

## Testing the Migration

### 1. Test Individual Endpoints
```bash
WEB_URL="https://your-service-url"

# Test daily summary
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-CloudScheduler: true" \
  "$WEB_URL/api/scheduler/daily-summary"

# Test market alerts
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-CloudScheduler: true" \
  "$WEB_URL/api/scheduler/market-alerts"

# Test database cleanup
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-CloudScheduler: true" \
  -d '{"days_to_keep": 90}' \
  "$WEB_URL/api/scheduler/cleanup-database"
```

### 2. Test Cloud Scheduler Jobs
```bash
# Run jobs manually
gcloud scheduler jobs run newsmonitor-daily-summary --location=$REGION
gcloud scheduler jobs run newsmonitor-market-alerts --location=$REGION

# Check job status
gcloud scheduler jobs list --location=$REGION

# View job logs
gcloud logging read 'resource.type="cloud_scheduler_job"' --limit=10
```

### 3. Monitor Application Logs
```bash
# Monitor scheduler endpoint calls
gcloud logging read 'resource.type="cloud_run_revision" AND httpRequest.requestUrl:"/api/scheduler/"' --limit=20

# Check for errors
gcloud logging read 'resource.type="cloud_run_revision" AND severity>=ERROR' --limit=10
```

## Rollback Plan

If issues arise, you can rollback to the Celery architecture:

### 1. Quick Rollback (Keep Both Systems)
```bash
# Pause Cloud Scheduler jobs
gcloud scheduler jobs pause newsmonitor-daily-summary --location=$REGION
gcloud scheduler jobs pause newsmonitor-weekly-summary --location=$REGION

# Redeploy original Celery-based service
gcloud run services replace deployment/cloud-run/web-service.yaml --region=$REGION
gcloud run services replace deployment/cloud-run/celery-service.yaml --region=$REGION
```

### 2. Full Rollback (Restore Redis)
```bash
# Recreate Redis instance
gcloud redis instances create newsmonitor-redis \
  --size=1 --region=$REGION --network=newsmonitor-vpc

# Redeploy Celery worker
./build-and-deploy.sh  # Original script
```

## Monitoring and Maintenance

### Cloud Scheduler Monitoring
```bash
# Job execution status
gcloud scheduler jobs describe JOB_NAME --location=$REGION

# Job execution history
gcloud logging read 'resource.type="cloud_scheduler_job" AND resource.labels.job_id="JOB_NAME"'
```

### Cost Monitoring
```bash
# Check current month costs
gcloud billing projects describe $PROJECT_ID

# Set up budget alerts
gcloud billing budgets create \
  --billing-account=$BILLING_ACCOUNT \
  --display-name="NewsMonitor Optimized Budget" \
  --budget-amount=150
```

## Success Metrics

### Cost Metrics
- [ ] Monthly GCP bill reduced by $42-82
- [ ] Redis charges eliminated
- [ ] Celery worker charges eliminated
- [ ] VPC connector costs reduced

### Functional Metrics
- [ ] Daily email summaries sent successfully
- [ ] Weekly email summaries sent successfully
- [ ] Market alerts triggered during market hours
- [ ] Database cleanup executed weekly
- [ ] Crawler execution every 3 hours
- [ ] All HTTP endpoints respond correctly

### Operational Metrics
- [ ] Simplified monitoring (fewer services)
- [ ] Reduced maintenance overhead
- [ ] Improved debugging capabilities
- [ ] Better error visibility

## Conclusion

The migration from Celery to Cloud Scheduler provides significant cost savings while maintaining all functionality. The HTTP endpoint approach is simpler, more reliable, and easier to maintain than the traditional Celery/Redis architecture.

**Estimated Timeline**: 3-5 hours for complete migration
**Estimated Savings**: $42-82/month (34-29% cost reduction)
**Risk Level**: Low (easy rollback, same functionality)

This migration is recommended for all NewsMonitor deployments seeking cost optimization and architectural simplification.
